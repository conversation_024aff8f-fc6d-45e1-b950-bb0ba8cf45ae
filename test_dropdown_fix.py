#!/usr/bin/env python3
"""
Test script to verify dropdown text visibility fix
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from modern_ui import ModernColors, ModernApp
    from ttkthemes import ThemedTk
    
    def test_dropdown_visibility():
        """Test dropdown text visibility"""
        print("Testing dropdown text visibility...")
        
        # Create test window
        root = ThemedTk(theme="arc")
        root.title("Dropdown Visibility Test")
        root.geometry("400x300")
        root.configure(bg=ModernColors.BG_PRIMARY)
        
        # Apply the same theme fixes as ModernApp
        style = ttk.Style()
        
        # Apply the dropdown fix
        style.configure("Modern.TCombobox",
                       fieldbackground="white",
                       foreground="black",
                       selectbackground=ModernColors.PRIMARY,
                       selectforeground="white")
        
        style.map("Modern.TCombobox",
                 fieldbackground=[('readonly', 'white'), ('active', 'white')],
                 selectbackground=[('readonly', ModernColors.PRIMARY), ('active', ModernColors.PRIMARY_HOVER)],
                 selectforeground=[('readonly', 'white'), ('active', 'white')],
                 foreground=[('readonly', 'black'), ('active', 'black'), ('focus', 'black')])
        
        # Configure dropdown list
        root.option_add('*TCombobox*Listbox.foreground', 'black')
        root.option_add('*TCombobox*Listbox.background', 'white')
        root.option_add('*TCombobox*Listbox.selectForeground', 'white')
        root.option_add('*TCombobox*Listbox.selectBackground', ModernColors.PRIMARY)
        
        # Create test frame
        test_frame = tk.Frame(root, bg=ModernColors.BG_PRIMARY)
        test_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            test_frame,
            text="Dropdown Visibility Test",
            bg=ModernColors.BG_PRIMARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Test dropdown
        test_label = tk.Label(
            test_frame,
            text="Test Dropdown (text should be visible):",
            bg=ModernColors.BG_PRIMARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=("Segoe UI", 12)
        )
        test_label.pack(anchor="w", pady=(0, 5))
        
        test_values = [
            "Option 1 - Should be visible",
            "Option 2 - Black text on white",
            "Option 3 - Easy to read",
            "Option 4 - No more invisible text!",
            "Option 5 - Perfect visibility"
        ]
        
        test_var = tk.StringVar(value=test_values[0])
        test_combo = ttk.Combobox(
            test_frame,
            textvariable=test_var,
            values=test_values,
            state="readonly",
            style="Modern.TCombobox",
            width=40
        )
        test_combo.pack(pady=(0, 20))
        
        # Instructions
        instructions = tk.Label(
            test_frame,
            text="Instructions:\n1. Click the dropdown arrow\n2. Verify all text is visible (black on white)\n3. Select different options\n4. Confirm selected text is visible",
            bg=ModernColors.BG_PRIMARY,
            fg=ModernColors.TEXT_SECONDARY,
            font=("Segoe UI", 10),
            justify=tk.LEFT
        )
        instructions.pack(anchor="w", pady=(0, 20))
        
        # Close button
        close_btn = tk.Button(
            test_frame,
            text="✅ Test Complete - Close",
            command=root.destroy,
            bg=ModernColors.PRIMARY,
            fg="white",
            font=("Segoe UI", 11, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        close_btn.pack()
        
        print("✅ Test window created successfully!")
        print("📋 Please verify dropdown text is visible and readable")
        
        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"400x300+{x}+{y}")
        
        root.mainloop()
        
        return True

    if __name__ == "__main__":
        print("🔧 Testing dropdown visibility fix...")
        try:
            test_dropdown_visibility()
            print("✅ Dropdown visibility test completed!")
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure all dependencies are installed and the src directory exists.")
