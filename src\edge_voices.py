# List of all English voices available in Edge TTS

EDGE_VOICES = [
    "en-AU-NatashaNeural", # Microsoft Natasha Online -Natural - English -Australia,
    "en-AU-WilliamNeural", # Microsoft William Online -Natural - English -Australia,
    "en-CA-ClaraNeural", # Microsoft Clara Online -Natural - English -Canada,
    "en-CA-LiamNeural", # Microsoft Liam Online -Natural - English -Canada,
    "en-HK-YanNeural", # Microsoft Yan Online -Natural - English -Hong Kong SAR,
    "en-HK-SamNeural", # Microsoft Sam Online -Natural - English -Hongkong,
    "en-IN-NeerjaExpressiveNeural", # Microsoft Neerja Online -Natural - English -India -Preview,
    "en-IN-NeerjaNeural", # Microsoft Neerja Online -Natural - English -India,
    "en-IN-PrabhatNeural", # Microsoft Prabhat Online -Natural - English -India,
    "en-IE-ConnorNeural", # Microsoft Connor Online -Natural - English -Ireland,
    "en-IE-EmilyNeural", # Microsoft Emily Online -Natural - English -Ireland,
    "en-KE-AsiliaNeural", # Microsoft Asilia Online -Natural - English -Kenya,
    "en-KE-ChilembaNeural", # Microsoft Chilemba Online -Natural - English -Kenya,
    "en-NZ-MitchellNeural", # Microsoft Mitchell Online -Natural - English -New Zealand,
    "en-NZ-MollyNeural", # Microsoft Molly Online -Natural - English -New Zealand,
    "en-NG-AbeoNeural", # Microsoft Abeo Online -Natural - English -Nigeria,
    "en-NG-EzinneNeural", # Microsoft Ezinne Online -Natural - English -Nigeria,
    "en-PH-JamesNeural", # Microsoft James Online -Natural - English -Philippines,
    "en-PH-RosaNeural", # Microsoft Rosa Online -Natural - English -Philippines,
    "en-US-AvaNeural", # Microsoft Ava Online -Natural - English -United States,
    "en-US-AndrewNeural", # Microsoft Andrew Online -Natural - English -United States,
    "en-US-EmmaNeural", # Microsoft Emma Online -Natural - English -United States,
    "en-US-BrianNeural", # Microsoft Brian Online -Natural - English -United States,
    "en-SG-LunaNeural", # Microsoft Luna Online -Natural - English -Singapore,
    "en-SG-WayneNeural", # Microsoft Wayne Online -Natural - English -Singapore,
    "en-ZA-LeahNeural", # Microsoft Leah Online -Natural - English -South Africa,
    "en-ZA-LukeNeural", # Microsoft Luke Online -Natural - English -South Africa,
    "en-TZ-ElimuNeural", # Microsoft Elimu Online -Natural - English -Tanzania,
    "en-TZ-ImaniNeural", # Microsoft Imani Online -Natural - English -Tanzania,
    "en-GB-LibbyNeural", # Microsoft Libby Online -Natural - English -United Kingdom,
    "en-GB-MaisieNeural", # Microsoft Maisie Online -Natural - English -United Kingdom,
    "en-GB-RyanNeural", # Microsoft Ryan Online -Natural - English -United Kingdom,
    "en-GB-SoniaNeural", # Microsoft Sonia Online -Natural - English -United Kingdom,
    "en-GB-ThomasNeural", # Microsoft Thomas Online -Natural - English -United Kingdom,
    "en-US-AnaNeural", # Microsoft Ana Online -Natural - English -United States,
    "en-US-AndrewMultilingualNeural", # Microsoft AndrewMultilingual Online -Natural - English -United States,
    "en-US-AriaNeural", # Microsoft Aria Online -Natural - English -United States,
    "en-US-AvaMultilingualNeural", # Microsoft AvaMultilingual Online -Natural - English -United States,
    "en-US-BrianMultilingualNeural", # Microsoft BrianMultilingual Online -Natural - English -United States,
    "en-US-ChristopherNeural", # Microsoft Christopher Online -Natural - English -United States,
    "en-US-EmmaMultilingualNeural", # Microsoft EmmaMultilingual Online -Natural - English -United States,
    "en-US-EricNeural", # Microsoft Eric Online -Natural - English -United States,
    "en-US-GuyNeural", # Microsoft Guy Online -Natural - English -United States,
    "en-US-JennyNeural", # Microsoft Jenny Online -Natural - English -United States,
    "en-US-MichelleNeural", # Microsoft Michelle Online -Natural - English -United States,
    "en-US-RogerNeural", # Microsoft Roger Online -Natural - English -United States,
    "en-US-SteffanNeural", # Microsoft Steffan Online -Natural - English -United States
]

def get_voices_by_locale(locale=None):
    """Get voices filtered by locale (e.g., 'en-US', 'en-GB')
    If locale is None, returns all voices
    """
    if locale is None:
        return EDGE_VOICES
    return [voice for voice in EDGE_VOICES if voice.startswith(locale)]
