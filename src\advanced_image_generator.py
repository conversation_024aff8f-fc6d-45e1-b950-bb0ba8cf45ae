import os
import re
from typing import Optional, Dict, Any, List, Callable
from utils import create_blank_image
import string

# Mapping from UI story types to internal story types
def map_story_type(ui_story_type: str) -> str:
    """
    Map UI story type to internal story type used by the image generator.

    Args:
        ui_story_type: Story type from the UI

    Returns:
        Corresponding internal story type
    """
    # Convert to lowercase for case-insensitive matching
    ui_type_lower = ui_story_type.lower()

    # Direct mappings
    direct_map = {
        "scary": "scary",
        "mystery": "mystery",
        "bedtime": "bedtime",
        "love": "love",
        "philosophy": "philosophy",
        "motivational": "motivational",
        "pet tips": "pet tips",
        "islamic": "islamic"
    }

    # If there's a direct match, return it
    if ui_type_lower in direct_map:
        return direct_map[ui_type_lower]

    # Category-based mappings
    if any(keyword in ui_type_lower for keyword in ["psychology", "mental", "mind", "brain", "behavior"]):
        return "psychology"

    if any(keyword in ui_type_lower for keyword in ["philosophy", "philosophical", "ethics", "moral", "existence"]):
        return "philosophy"

    if any(keyword in ui_type_lower for keyword in ["motivational", "motivation", "inspire", "success", "achievement", "goals", "fitness", "money", "saving", "tips", "hacks", "advice"]):
        return "motivational"

    if any(keyword in ui_type_lower for keyword in ["love", "relationship", "dating", "romance", "couple"]):
        return "love"

    if any(keyword in ui_type_lower for keyword in ["scary", "horror", "fear", "terror", "creepy", "spooky", "crime", "conspiracy"]):
        return "scary"

    if any(keyword in ui_type_lower for keyword in ["mystery", "detective", "puzzle", "solve", "clue", "urban legends"]):
        return "mystery"

    if any(keyword in ui_type_lower for keyword in ["bedtime", "sleep", "night", "rest", "relax", "calm", "soothing"]):
        return "bedtime"

    if any(keyword in ui_type_lower for keyword in ["pet", "animal", "dog", "cat", "bird", "fish"]):
        return "pet tips"

    if any(keyword in ui_type_lower for keyword in ["islamic", "islam", "hadith", "quran", "muslim"]):
        return "islamic"

    # Default to "general" which will use a combination of psychology and motivational concepts
    return "general"



# Common English stopwords to filter out
COMMON_STOPWORDS = {
    "a", "an", "the", "and", "or", "but", "if", "because", "as", "what", "when",
    "where", "how", "who", "which", "this", "that", "these", "those", "then",
    "just", "so", "than", "such", "both", "through", "about", "for", "is", "of",
    "while", "during", "to", "from", "in", "out", "on", "off", "over", "under",
    "again", "further", "then", "once", "here", "there", "all", "any", "both",
    "each", "few", "more", "most", "other", "some", "such", "no", "nor", "not",
    "only", "own", "same", "too", "very", "can", "will", "should", "now", "i",
    "me", "my", "myself", "we", "our", "ours", "ourselves", "you", "your", "yours",
    "yourself", "yourselves", "he", "him", "his", "himself", "she", "her", "hers",
    "herself", "it", "its", "itself", "they", "them", "their", "theirs", "themselves",
    "am", "are", "was", "were", "be", "been", "being", "have", "has", "had", "having",
    "do", "does", "did", "doing", "would", "should", "could", "ought", "i'm", "you're",
    "he's", "she's", "it's", "we're", "they're", "i've", "you've", "we've", "they've",
    "i'd", "you'd", "he'd", "she'd", "we'd", "they'd", "i'll", "you'll", "he'll", "she'll",
    "we'll", "they'll", "isn't", "aren't", "wasn't", "weren't", "hasn't", "haven't", "hadn't",
    "doesn't", "don't", "didn't", "won't", "wouldn't", "shan't", "shouldn't", "can't", "cannot",
    "couldn't", "mustn't", "let's", "that's", "who's", "what's", "here's", "there's", "when's",
    "why's", "how's"
}

def extract_key_concepts(text: str) -> List[str]:
    """
    Extract key concepts from text using simple text processing techniques.

    Args:
        text: The text to analyze

    Returns:
        List of key concepts
    """
    # Convert to lowercase and split into words
    words = text.lower().split()

    # Remove punctuation
    words = [word.strip(string.punctuation) for word in words if word.strip(string.punctuation)]

    # Remove stopwords
    words = [word for word in words if word not in COMMON_STOPWORDS and len(word) > 2]

    # Create simple bigrams (pairs of adjacent words)
    bigrams = []
    for i in range(len(words) - 1):
        # Only create bigrams with meaningful words
        if words[i] not in COMMON_STOPWORDS and words[i+1] not in COMMON_STOPWORDS:
            bigrams.append(f"{words[i]} {words[i+1]}")

    # Create simple trigrams (triplets of adjacent words)
    trigrams = []
    for i in range(len(words) - 2):
        # Only create trigrams with meaningful words
        if (words[i] not in COMMON_STOPWORDS and
            words[i+1] not in COMMON_STOPWORDS and
            words[i+2] not in COMMON_STOPWORDS):
            trigrams.append(f"{words[i]} {words[i+1]} {words[i+2]}")

    # Combine trigrams, bigrams, and individual words, prioritizing longer phrases
    all_concepts = trigrams + bigrams + words

    # Remove duplicates while preserving order
    seen = set()
    unique_concepts = [x for x in all_concepts if not (x in seen or seen.add(x))]

    return unique_concepts[:10]  # Return top 10 concepts

def extract_visual_elements(description: str) -> Dict[str, List[str]]:
    """
    Extract detailed visual elements from a scene description.

    Args:
        description: The full scene description

    Returns:
        Dictionary of visual elements by category
    """
    visual_elements = {
        "setting": [],
        "subjects": [],
        "actions": [],
        "lighting": [],
        "colors": [],
        "composition": [],
        "camera": [],
        "mood": [],
        "time_of_day": [],
        "weather": []
    }

    # Setting patterns
    setting_patterns = [
        r'(?:in|at|on) (?:a|an|the) ([^,.]+)',
        r'setting:? ([^,.]+)',
        r'environment:? ([^,.]+)',
        r'location:? ([^,.]+)',
        r'scene (?:shows|depicts|features) ([^,.]+)',
        r'backdrop of ([^,.]+)',
    ]

    # Subject patterns
    subject_patterns = [
        r'(?:a|an|the) ([^,.]+? (?:person|man|woman|child|individual|figure|character|subject|people|group|audience|crowd))',
        r'([^,.]+? (?:person|man|woman|child|individual|figure|character|subject|people|group|audience|crowd)) (?:is|are|appears|seem)',
    ]

    # Action patterns
    action_patterns = [
        r'(?:person|man|woman|child|individual|figure|character|subject|people|group) (?:is|are) ([^,.]+?ing)',
        r'([^,.]+?ing) (?:the|a|an) (?:object|item|scene|view|environment)',
    ]

    # Lighting patterns
    lighting_patterns = [
        r'lighting (?:is|appears) ([^,.]+)',
        r'lit by ([^,.]+)',
        r'light(?:ing)? (?:is|appears) ([^,.]+)',
        r'([^,.]+) lighting',
        r'([^,.]+) light',
        r'illuminated by ([^,.]+)',
    ]

    # Color patterns
    color_patterns = [
        r'colors? (?:is|are|of) ([^,.]+)',
        r'([^,.]+) colors?',
        r'color palette (?:is|of) ([^,.]+)',
        r'hues of ([^,.]+)',
        r'tones of ([^,.]+)',
        r'palette of ([^,.]+)',
    ]

    # Composition patterns
    composition_patterns = [
        r'composition (?:is|shows) ([^,.]+)',
        r'framed ([^,.]+)',
        r'arranged ([^,.]+)',
        r'positioned ([^,.]+)',
        r'layout (?:is|shows) ([^,.]+)',
    ]

    # Camera patterns
    camera_patterns = [
        r'camera (?:is|shows|captures) ([^,.]+)',
        r'shot (?:is|from) ([^,.]+)',
        r'angle (?:is|from) ([^,.]+)',
        r'perspective (?:is|from) ([^,.]+)',
        r'view (?:is|from) ([^,.]+)',
        r'(?:close-up|medium shot|wide shot|overhead|aerial) ([^,.]+)',
    ]

    # Mood patterns
    mood_patterns = [
        r'mood (?:is|appears) ([^,.]+)',
        r'atmosphere (?:is|appears) ([^,.]+)',
        r'feeling of ([^,.]+)',
        r'sense of ([^,.]+)',
        r'evoking ([^,.]+)',
        r'creating (?:a|an) ([^,.]+) (?:mood|atmosphere|feeling|sense)',
    ]

    # Time of day patterns
    time_patterns = [
        r'(?:morning|afternoon|evening|night|dawn|dusk|daylight|nighttime)',
        r'(?:early|late) ([^,.]+)',
        r'time of day (?:is|appears) ([^,.]+)',
    ]

    # Weather patterns
    weather_patterns = [
        r'(?:sunny|cloudy|rainy|stormy|snowy|foggy|misty|clear|overcast)',
        r'weather (?:is|appears) ([^,.]+)',
        r'(?:rain|snow|fog|mist|clouds|sunshine) ([^,.]+)',
    ]

    # Extract elements using patterns
    pattern_categories = [
        ("setting", setting_patterns),
        ("subjects", subject_patterns),
        ("actions", action_patterns),
        ("lighting", lighting_patterns),
        ("colors", color_patterns),
        ("composition", composition_patterns),
        ("camera", camera_patterns),
        ("mood", mood_patterns),
        ("time_of_day", time_patterns),
        ("weather", weather_patterns),
    ]

    for category, patterns in pattern_categories:
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            if matches:
                for match in matches:
                    if isinstance(match, tuple):
                        visual_elements[category].append(match[-1].strip())
                    else:
                        visual_elements[category].append(match.strip())

    # If we couldn't extract specific elements, use key sentences
    if all(len(elements) == 0 for elements in visual_elements.values()):
        sentences = re.split(r'[.!?]', description)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        # Prioritize sentences with visual words
        visual_words = ['shows', 'displays', 'depicts', 'features', 'contains', 'with', 'scene', 'image', 'camera', 'shot', 'angle', 'lighting', 'color']
        visual_sentences = [s for s in sentences if any(word in s.lower() for word in visual_words)]

        if visual_sentences:
            visual_elements["setting"] = visual_sentences[:2]
        else:
            visual_elements["setting"] = sentences[:2]

    return visual_elements

def extract_subtitle_concepts(subtitles: str, story_type: str) -> Dict[str, List[str]]:
    """
    Extract key concepts from subtitles based on story type.

    Args:
        subtitles: The subtitle text
        story_type: The type of story (e.g., love, psychology, motivational)

    Returns:
        Dictionary of concepts by category
    """
    # Remove speaker labels if present
    clean_subtitles = re.sub(r'^[^:]+:\s*', '', subtitles)

    # Initialize concept categories
    concepts = {
        "main_subjects": [],
        "actions": [],
        "emotions": [],
        "abstract_concepts": [],
        "metaphors": [],
        "visual_analogies": []
    }

    # Simple text processing approach
    words = clean_subtitles.lower().split()
    # Remove punctuation
    words = [word.strip(string.punctuation) for word in words if word.strip(string.punctuation)]
    # Remove stopwords
    words = [word for word in words if word not in COMMON_STOPWORDS and len(word) > 2]

    # Identify potential nouns (main subjects) - words that don't end with 'ing', 'ed', 'ly'
    potential_nouns = [word for word in words if not (word.endswith('ing') or word.endswith('ed') or word.endswith('ly'))]
    concepts["main_subjects"] = list(set(potential_nouns))[:5]  # Top 5 unique potential nouns

    # Identify potential verbs (actions) - words that end with 'ing' or 'ed'
    potential_verbs = [word for word in words if word.endswith('ing') or word.endswith('ed')]
    concepts["actions"] = list(set(potential_verbs))[:5]  # Top 5 unique potential verbs

    # If we don't have enough actions, add some common action words
    if len(concepts["actions"]) < 3:
        # Add some words from the text that might be actions
        additional_actions = [word for word in words if word not in concepts["main_subjects"] and word not in concepts["actions"]][:3]
        concepts["actions"].extend(additional_actions)

    # Extract emotion words
    emotion_words = [
        "happy", "sad", "angry", "afraid", "surprised", "disgusted", "excited",
        "calm", "content", "anxious", "nervous", "relaxed", "stressed", "peaceful",
        "joyful", "depressed", "frustrated", "curious", "bored", "interested",
        "love", "hate", "fear", "hope", "despair", "confidence", "doubt"
    ]
    concepts["emotions"] = [word for word in words if word in emotion_words]

    # Extract abstract concepts based on story type
    abstract_concept_map = {
        "psychology": [
            "mind", "behavior", "cognition", "perception", "memory", "attention",
            "emotion", "motivation", "personality", "development", "social", "influence",
            "bias", "thinking", "learning", "conditioning", "unconscious", "conscious",
            "priming", "association", "mental", "psychological", "therapy", "treatment"
        ],
        "philosophy": [
            "existence", "knowledge", "truth", "meaning", "value", "reason", "mind",
            "language", "consciousness", "morality", "ethics", "justice", "freedom",
            "identity", "reality", "perception", "logic", "fallacy", "argument",
            "metaphysics", "epistemology", "ontology", "phenomenology", "existentialism"
        ],
        "motivational": [
            "success", "achievement", "goal", "dream", "inspiration", "motivation",
            "determination", "perseverance", "courage", "strength", "overcome",
            "challenge", "obstacle", "growth", "improvement", "progress", "potential",
            "purpose", "passion", "vision", "mindset", "attitude", "belief", "confidence"
        ],
        "love": [
            "love", "relationship", "romance", "passion", "intimacy", "commitment",
            "connection", "attraction", "affection", "emotion", "feeling", "heart",
            "partner", "couple", "marriage", "dating", "trust", "communication",
            "understanding", "support", "care", "compassion", "empathy", "bond"
        ],
        "scary": [
            "fear", "terror", "horror", "dread", "panic", "anxiety", "suspense",
            "tension", "threat", "danger", "risk", "darkness", "unknown", "supernatural",
            "monster", "ghost", "shadow", "nightmare", "scream", "shock", "surprise",
            "jump", "creepy", "eerie", "spooky", "haunted", "mysterious", "sinister"
        ],
        "mystery": [
            "mystery", "puzzle", "clue", "evidence", "investigation", "detective",
            "case", "crime", "suspect", "witness", "secret", "hidden", "reveal",
            "discover", "uncover", "solve", "question", "answer", "truth", "lie",
            "deception", "twist", "surprise", "unexpected", "suspense", "intrigue"
        ],
        "bedtime": [
            "sleep", "dream", "night", "rest", "calm", "peaceful", "quiet", "gentle",
            "soft", "warm", "cozy", "comfort", "relax", "soothe", "lullaby", "story",
            "imagination", "fantasy", "wonder", "magic", "enchant", "fairy", "tale",
            "adventure", "journey", "character", "lesson", "moral", "wisdom"
        ],
        "pet tips": [
            "pet", "animal", "dog", "cat", "bird", "fish", "care", "health", "food",
            "nutrition", "exercise", "training", "behavior", "grooming", "veterinarian",
            "medicine", "treatment", "play", "toy", "enrichment", "environment",
            "habitat", "safety", "protection", "love", "bond", "relationship", "companion"
        ]
    }

    # Get abstract concepts for the specific story type, or use a default set
    story_type_lower = story_type.lower()
    abstract_concepts = abstract_concept_map.get(story_type_lower, [])

    # If no specific story type match, use a combination of common concepts
    if not abstract_concepts:
        abstract_concepts = abstract_concept_map["psychology"] + abstract_concept_map["motivational"]

    # Find abstract concepts in the subtitles
    concepts["abstract_concepts"] = [word for word in words if word in abstract_concepts]

    # Generate potential visual metaphors based on abstract concepts
    metaphor_map = {
        "mind": ["brain", "maze", "computer", "network", "garden"],
        "growth": ["plant", "tree", "seedling", "mountain climbing", "steps"],
        "connection": ["bridge", "hands touching", "web", "rope", "puzzle pieces"],
        "freedom": ["bird", "open sky", "broken chains", "open door", "wings"],
        "time": ["clock", "hourglass", "seasons changing", "river flowing", "path"],
        "knowledge": ["book", "light bulb", "key", "open door", "library"],
        "emotion": ["heart", "ocean waves", "weather", "colors", "facial expressions"],
        "struggle": ["climbing", "weights", "obstacle course", "storm", "maze"],
        "success": ["summit", "trophy", "finish line", "sunrise", "open door"],
        "fear": ["shadow", "dark forest", "closed door", "cliff edge", "chains"],
        "love": ["heart", "intertwined trees", "two birds", "connected hands", "bridge"],
        "change": ["butterfly", "seasons", "river", "road", "crossroads"],
        "balance": ["scale", "yin-yang", "tightrope", "stones stacked", "seesaw"],
        "conflict": ["storm", "fire and water", "opposing forces", "tug of war", "chess"],
        "hope": ["light in darkness", "sunrise", "seedling", "open window", "star"],
        "mystery": ["fog", "partially open door", "maze", "shadow", "question mark"],
        "peace": ["still water", "dove", "calm landscape", "balanced stones", "sunset"]
    }

    # Find potential metaphors for concepts in the subtitles
    for concept in concepts["abstract_concepts"]:
        if concept in metaphor_map:
            concepts["metaphors"].extend(metaphor_map[concept])

    # Generate visual analogies based on story type
    visual_analogy_map = {
        "psychology": [
            "brain with visible thought patterns",
            "maze representing mental processes",
            "mirror reflecting different versions of self",
            "layers of consciousness as geological strata",
            "behavioral patterns shown as connecting dots"
        ],
        "philosophy": [
            "cave with shadows (Plato's allegory)",
            "fork in the road representing choice",
            "scales of justice",
            "tree of knowledge",
            "infinite regression of mirrors"
        ],
        "motivational": [
            "mountain climb representing achievement",
            "phoenix rising from ashes",
            "marathon runner approaching finish line",
            "seedling growing through concrete",
            "lighthouse beam cutting through storm"
        ],
        "love": [
            "two trees with intertwining branches",
            "bridge connecting two islands",
            "two puzzle pieces fitting together",
            "dance of complementary elements",
            "nested objects showing belonging"
        ],
        "scary": [
            "long shadow stretching from small object",
            "door slightly ajar with darkness beyond",
            "reflection showing something not present",
            "empty playground with single moving swing",
            "familiar object with subtle wrongness"
        ],
        "mystery": [
            "fog with partial visibility",
            "magnifying glass revealing hidden detail",
            "puzzle with missing pieces",
            "path disappearing into unknown",
            "object casting multiple contradictory shadows"
        ],
        "bedtime": [
            "starry night sky with constellation stories",
            "cozy nest with sleeping creatures",
            "dreamcatcher with flowing elements",
            "gentle transition from waking to dreaming",
            "protective cocoon of comfort"
        ],
        "pet tips": [
            "human-animal bond shown as connected hearts",
            "paw prints alongside human footprints",
            "nurturing hands supporting animal",
            "home environment optimized for pet wellbeing",
            "communication bridge between species"
        ],
        "islamic": [
            "geometric patterns representing divine order",
            "light and shadow symbolizing guidance",
            "open book with illuminated pages",
            "architectural elements from Islamic tradition",
            "calligraphy-inspired design elements"
        ]
    }

    # Add visual analogies for the specific story type
    concepts["visual_analogies"] = visual_analogy_map.get(story_type_lower, [])

    return concepts

def create_advanced_image_prompt(
    description: str,
    subtitles: str,
    story_type: str,
    image_style: str
) -> str:
    """
    Create an advanced image prompt that combines scene description and subtitle concepts.

    Args:
        description: The scene description
        subtitles: The subtitles for the scene
        story_type: The type of story
        image_style: The desired image style

    Returns:
        A detailed image prompt
    """
    # Extract visual elements from description
    visual_elements = extract_visual_elements(description)

    # Extract concepts from subtitles
    subtitle_concepts = extract_subtitle_concepts(subtitles, story_type)

    # Build the core scene description
    scene_parts = []

    # Add setting
    if visual_elements["setting"]:
        scene_parts.append(f"Setting: {', '.join(visual_elements['setting'][:2])}")

    # Add subjects
    if visual_elements["subjects"]:
        scene_parts.append(f"Subjects: {', '.join(visual_elements['subjects'][:2])}")
    elif subtitle_concepts["main_subjects"]:
        scene_parts.append(f"Subjects: {', '.join(subtitle_concepts['main_subjects'][:2])}")

    # Add actions
    if visual_elements["actions"]:
        scene_parts.append(f"Actions: {', '.join(visual_elements['actions'][:2])}")
    elif subtitle_concepts["actions"]:
        scene_parts.append(f"Actions: {', '.join(subtitle_concepts['actions'][:2])}")

    # Add camera and composition
    camera_comp_parts = []
    if visual_elements["camera"]:
        camera_comp_parts.append(f"camera angle: {visual_elements['camera'][0]}")
    if visual_elements["composition"]:
        camera_comp_parts.append(f"composition: {visual_elements['composition'][0]}")

    if camera_comp_parts:
        scene_parts.append(f"Visual style: {', '.join(camera_comp_parts)}")

    # Add lighting and colors
    visual_mood_parts = []
    if visual_elements["lighting"]:
        visual_mood_parts.append(f"lighting: {visual_elements['lighting'][0]}")
    if visual_elements["colors"]:
        visual_mood_parts.append(f"color palette: {visual_elements['colors'][0]}")
    if visual_elements["mood"]:
        visual_mood_parts.append(f"mood: {visual_elements['mood'][0]}")

    if visual_mood_parts:
        scene_parts.append(f"Atmosphere: {', '.join(visual_mood_parts)}")

    # Add abstract concepts and metaphors for more conceptual depth
    concept_parts = []

    # Add key abstract concepts from subtitles
    if subtitle_concepts["abstract_concepts"]:
        concept_parts.append(f"representing the concept of {', '.join(subtitle_concepts['abstract_concepts'][:3])}")

    # Add visual metaphors if available
    if subtitle_concepts["metaphors"]:
        concept_parts.append(f"visual metaphor using {', '.join(subtitle_concepts['metaphors'][:2])}")

    # Add visual analogies if available
    if subtitle_concepts["visual_analogies"]:
        concept_parts.append(f"showing {subtitle_concepts['visual_analogies'][0]}")

    # Combine all parts into a cohesive prompt
    core_scene = ". ".join(scene_parts)

    # Add conceptual elements if available
    if concept_parts:
        conceptual_layer = " " + ". ".join(concept_parts)
    else:
        conceptual_layer = ""

    # Create the final prompt with style-specific enhancements
    image_style_lower = image_style.lower()

    # Map of all image styles to their prompt templates
    style_prompts = {
        "photorealistic": f"Highly detailed, photorealistic 8K image of {core_scene}{conceptual_layer}. Hyperrealistic, professional photography, perfect lighting, ultra-detailed, photographic quality.",

        "cinematic": f"Highly detailed, cinematic 8K image of {core_scene}{conceptual_layer}. Professional lighting, masterful composition, dramatic atmosphere, high-quality production, movie still quality.",

        "anime": f"Japanese anime style illustration of {core_scene}{conceptual_layer}. Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic.",

        "comic book": f"Professional comic book illustration of {core_scene}{conceptual_layer}. Dynamic poses, bold outlines, vibrant colors, Marvel/DC style, detailed shading and crosshatching, rich textures, action-packed composition.",

        "comic-book": f"Professional comic book illustration of {core_scene}{conceptual_layer}. Dynamic poses, bold outlines, vibrant colors, Marvel/DC style, detailed shading and crosshatching, rich textures, action-packed composition.",

        "pixel art": f"Detailed pixel art of {core_scene}{conceptual_layer}. 16-bit style, limited color palette, crisp edges, nostalgic retro gaming aesthetic, charming design style.",

        "pixel-art": f"Detailed pixel art of {core_scene}{conceptual_layer}. 16-bit style, limited color palette, crisp edges, nostalgic retro gaming aesthetic, charming design style.",

        "pixar art": f"3D animated Pixar style rendering of {core_scene}{conceptual_layer}. Characteristic Pixar aesthetic, expressive characters, soft lighting, vibrant colors, playful design, high-quality 3D animation style.",

        "pixar-art": f"3D animated Pixar style rendering of {core_scene}{conceptual_layer}. Characteristic Pixar aesthetic, expressive characters, soft lighting, vibrant colors, playful design, high-quality 3D animation style.",

        "digital art": f"Professional digital art illustration of {core_scene}{conceptual_layer}. High resolution, vivid colors, detailed textures, fantasy art style, impressive light effects, cohesive composition.",

        "digital-art": f"Professional digital art illustration of {core_scene}{conceptual_layer}. High resolution, vivid colors, detailed textures, fantasy art style, impressive light effects, cohesive composition.",

        "oil painting": f"Fine art oil painting of {core_scene}{conceptual_layer}. Rich textures, visible brushstrokes, depth through glazing, classical composition, warm color palette, museum-quality, reminiscent of old masters.",

        "oil-painting": f"Fine art oil painting of {core_scene}{conceptual_layer}. Rich textures, visible brushstrokes, depth through glazing, classical composition, warm color palette, museum-quality, reminiscent of old masters.",

        "watercolor": f"Delicate watercolor painting of {core_scene}{conceptual_layer}. Soft color washes, flowing transitions, subtle bleeding edges, translucent layers, loose brushwork, organic feel, on textured paper.",

        "dark aesthetic": f"Dark moody atmosphere of {core_scene}{conceptual_layer}. Deep shadows and high contrast, low-key lighting, limited dark color palette, gothic elements, mysterious atmosphere, dramatic cinematic style.",

        "dark-aesthetic": f"Dark moody atmosphere of {core_scene}{conceptual_layer}. Deep shadows and high contrast, low-key lighting, limited dark color palette, gothic elements, mysterious atmosphere, dramatic cinematic style.",

        "neon cyberpunk": f"Futuristic cyberpunk scene of {core_scene}{conceptual_layer}. Bright neon lights in magenta and cyan, rainy reflective streets, advanced technology, dystopian urban setting, Blade Runner inspired, holographic displays, high-contrast lighting.",

        "neon-cyberpunk": f"Futuristic cyberpunk scene of {core_scene}{conceptual_layer}. Bright neon lights in magenta and cyan, rainy reflective streets, advanced technology, dystopian urban setting, Blade Runner inspired, holographic displays, high-contrast lighting.",

        "minimalist": f"Minimalist design of {core_scene}{conceptual_layer}. Simple geometric shapes, limited color palette, negative space, clean composition, essential elements only, modern aesthetic.",

        "film noir": f"Black and white film noir style of {core_scene}{conceptual_layer}. High contrast, dramatic shadows, moody lighting, 1940s aesthetic, cinematic composition, mysterious atmosphere.",

        "film-noir": f"Black and white film noir style of {core_scene}{conceptual_layer}. High contrast, dramatic shadows, moody lighting, 1940s aesthetic, cinematic composition, mysterious atmosphere.",

        "retro 80s": f"1980s retro style of {core_scene}{conceptual_layer}. Synthwave aesthetic, neon colors, grid patterns, sunset gradients, VHS quality, nostalgic 80s vibe, retro futurism.",

        "retro-80s": f"1980s retro style of {core_scene}{conceptual_layer}. Synthwave aesthetic, neon colors, grid patterns, sunset gradients, VHS quality, nostalgic 80s vibe, retro futurism.",

        "vaporwave": f"Vaporwave aesthetic of {core_scene}{conceptual_layer}. Pastel colors, glitch effects, Roman busts, checkerboard patterns, retro computing elements, 90s internet culture, surreal composition.",

        "cottagecore": f"Cottagecore aesthetic of {core_scene}{conceptual_layer}. Rustic countryside setting, warm natural lighting, wildflowers, vintage elements, cozy atmosphere, soft colors, pastoral romanticism.",

        "hyperrealistic": f"Ultra-detailed hyperrealistic image of {core_scene}{conceptual_layer}. Indistinguishable from photography, extreme textures and details, perfect lighting, flawless rendering, photographic realism.",

        "flat design": f"Modern flat design illustration of {core_scene}{conceptual_layer}. Clean vector style, solid colors without gradients, simplified shapes, minimalistic approach, 2D aesthetic, professional graphic design.",

        "flat-design": f"Modern flat design illustration of {core_scene}{conceptual_layer}. Clean vector style, solid colors without gradients, simplified shapes, minimalistic approach, 2D aesthetic, professional graphic design.",

        "3d cartoon": f"3D animated cartoon style of {core_scene}{conceptual_layer}. Pixar/Dreamworks quality, exaggerated proportions, smooth textures, vibrant colors, expressive characters, playful design.",

        "3d-cartoon": f"3D animated cartoon style of {core_scene}{conceptual_layer}. Pixar/Dreamworks quality, exaggerated proportions, smooth textures, vibrant colors, expressive characters, playful design.",

        "pastel dreamscape": f"Dreamy pastel fantasy landscape of {core_scene}{conceptual_layer}. Soft color palette, ethereal atmosphere, magical elements, hazy lighting, whimsical design, floating objects, surreal and enchanting.",

        "pastel-dreamscape": f"Dreamy pastel fantasy landscape of {core_scene}{conceptual_layer}. Soft color palette, ethereal atmosphere, magical elements, hazy lighting, whimsical design, floating objects, surreal and enchanting.",

        "fantasy vibrant": f"Vibrant fantasy illustration of {core_scene}{conceptual_layer}. Rich saturated colors, magical atmosphere, elaborate details, mythical elements, dramatic lighting, epic composition, high-quality digital artwork.",

        "fantasy-vibrant": f"Vibrant fantasy illustration of {core_scene}{conceptual_layer}. Rich saturated colors, magical atmosphere, elaborate details, mythical elements, dramatic lighting, epic composition, high-quality digital artwork.",

        "nostalgic filter": f"Nostalgic photograph with vintage filter of {core_scene}{conceptual_layer}. Warm amber glow, slight grain texture, light leaks, faded colors, 70s/80s photography style, authentic retro photography.",

        "nostalgic-filter": f"Nostalgic photograph with vintage filter of {core_scene}{conceptual_layer}. Warm amber glow, slight grain texture, light leaks, faded colors, 70s/80s photography style, authentic retro photography.",

        "vhs aesthetic": f"VHS style image of {core_scene}{conceptual_layer}. Tracking lines, chromatic aberration, interlaced scan lines, low resolution, bleeding colors, warped edges, 80s/90s camcorder look, authentic analog video aesthetic.",

        "vhs-aesthetic": f"VHS style image of {core_scene}{conceptual_layer}. Tracking lines, chromatic aberration, interlaced scan lines, low resolution, bleeding colors, warped edges, 80s/90s camcorder look, authentic analog video aesthetic.",

        "y2k": f"Y2K aesthetic of {core_scene}{conceptual_layer}. Early 2000s design, glossy metallic elements, bright neon colors, bubble shapes, futuristic yet nostalgic, digital artifacts, millennial aesthetic, authentic early digital era vibes.",

        "god anime vine": f"Bold, exaggerated God Anime Vine style of {core_scene}{conceptual_layer}. Dramatic anime aesthetic, intense expressions, vibrant saturated colors, extreme contrast, dynamic action poses, bold outlines, exaggerated features, viral social media aesthetic.",

        "god-anime-vine": f"Bold, exaggerated God Anime Vine style of {core_scene}{conceptual_layer}. Dramatic anime aesthetic, intense expressions, vibrant saturated colors, extreme contrast, dynamic action poses, bold outlines, exaggerated features, viral social media aesthetic.",

        "ghibli": f"Studio Ghibli style illustration of {core_scene}{conceptual_layer}. Soft watercolor-like textures, gentle color palette, whimsical characters, detailed natural environments, dreamy atmospheric lighting, hand-drawn animation quality, Miyazaki-inspired artistic sensibility."
    }

    # Get the prompt for the specified style, or use a default if not found
    prompt = style_prompts.get(image_style_lower, f"High-quality detailed image of {core_scene}{conceptual_layer}. Professional lighting, detailed composition, vivid colors.")

    # Add a reminder for no text
    prompt += " NO TEXT, no words, no writing, no captions, no labels."

    return prompt

def generate_and_download_images(
    storyboard_project: Dict[str, Any],
    story_dir: str,
    image_style: str,
    story_type: str,
    image_generator_func: Callable[[str], Optional[bytes]],
    video_quality: str = "720p",
    orientation: str = "portrait",
    check_pause_func=None,
    check_stop_func=None
) -> List[str]:
    """
    Generate and download images for each scene in the storyboard.

    Args:
        storyboard_project: The storyboard project data
        story_dir: Directory to save images
        image_style: Style for the images
        story_type: Type of story
        image_generator_func: Function to generate images
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)
        orientation: Orientation of the video (portrait, landscape)
        check_pause_func: Optional function to check if generation should pause
        check_stop_func: Optional function to check if generation should stop

    Returns:
        List of image file paths
    """
    # Map the UI story type to internal story type
    mapped_story_type = map_story_type(story_type)
    # Create directory for images if it doesn't exist
    os.makedirs(story_dir, exist_ok=True)

    image_files = []
    image_prompts = []

    # For each scene in the storyboard, generate an image
    for storyboard in storyboard_project['storyboards']:
        # Check if we should stop
        if check_stop_func and check_stop_func():
            print("Image generation stopped by user")
            return image_files

        # Check if we should pause
        if check_pause_func:
            check_pause_func()

        scene_number = storyboard['scene_number']

        # Get description and subtitles
        description = storyboard.get('description', '')
        subtitles = storyboard.get('subtitles', '')

        # Create advanced image prompt
        image_prompt = create_advanced_image_prompt(
            description=description,
            subtitles=subtitles,
            story_type=mapped_story_type,  # Use the mapped story type
            image_style=image_style
        )

        # Create a safe scene number for the image filename
        if isinstance(scene_number, str):
            safe_scene_num = re.sub(r'[^\w]', '_', scene_number)
        else:
            safe_scene_num = str(scene_number)

        # Define image filename with safe scene number
        image_filename = os.path.join(story_dir, f"scene_{safe_scene_num}.png")

        # Save the prompt for debugging
        image_prompts.append(image_prompt)
        print(f"Advanced image prompt for scene {scene_number}: {image_prompt}")

        # Try to generate image with the specified generator
        try:
            print(f"Generating image for scene {scene_number}")

            # Check again if we should stop before making the API call
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return image_files

            # Pass video quality and orientation to the image generator
            image_bytes = image_generator_func(image_prompt, orientation=orientation, video_quality=video_quality)

            # Check again if we should stop after the API call
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return image_files

            if image_bytes:
                with open(image_filename, "wb") as f:
                    f.write(image_bytes)
                print(f"Image saved to {image_filename}")
                image_files.append(image_filename)
            else:
                # If primary generator fails, try the replicate_flux_api as fallback
                from api import replicate_flux_api

                # Check again if we should stop before trying the fallback
                if check_stop_func and check_stop_func():
                    print("Image generation stopped by user")
                    return image_files

                if image_generator_func.__name__ != "replicate_flux_api":
                    print(f"Primary generator failed, trying replicate_flux_api as fallback")
                    image_bytes = replicate_flux_api(image_prompt, orientation=orientation, video_quality=video_quality)

                    # Check again if we should stop after the fallback API call
                    if check_stop_func and check_stop_func():
                        print("Image generation stopped by user")
                        return image_files

                    if image_bytes:
                        with open(image_filename, "wb") as f:
                            f.write(image_bytes)
                        print(f"Image saved to {image_filename} (using fallback)")
                        image_files.append(image_filename)
                    else:
                        print(f"Fallback generator also failed, creating blank image")
                        create_blank_image(image_filename)
                        image_files.append(image_filename)
                else:
                    print(f"Generator failed, creating blank image")
                    create_blank_image(image_filename)
                    image_files.append(image_filename)
        except Exception as e:
            print(f"Error generating image: {e}")
            create_blank_image(image_filename)
            image_files.append(image_filename)

    # Save the image prompts to a file for reference
    with open(os.path.join(story_dir, "image_prompts.txt"), "w", encoding="utf-8") as f:
        for i, prompt in enumerate(image_prompts):
            scene_num = i + 1
            f.write(f"Scene {scene_num}: {prompt}\n\n")

    print(f"Image prompts saved to {os.path.join(story_dir, 'image_prompts.txt')}")

    return image_files

def generate_image_for_custom_script(
    paragraph: str,
    image_style: str,
    story_type: str,
    image_generator_func: Callable[[str], Optional[bytes]],
    orientation: str = "portrait",
    video_quality: str = "720p"
) -> Optional[bytes]:
    """
    Generate an image for a custom script paragraph.

    Args:
        paragraph: The paragraph text
        image_style: Style for the image
        story_type: Type of story
        image_generator_func: Function to generate images
        orientation: Orientation of the video (portrait, landscape)
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)

    Returns:
        Image bytes if successful, None otherwise
    """
    # Map the UI story type to internal story type
    mapped_story_type = map_story_type(story_type)

    # Create a mock description based on the paragraph
    mock_description = f"A scene showing the key elements of: {paragraph}"

    # Create advanced image prompt
    image_prompt = create_advanced_image_prompt(
        description=mock_description,
        subtitles=paragraph,
        story_type=mapped_story_type,  # Use the mapped story type
        image_style=image_style
    )

    print(f"Advanced image prompt for custom script: {image_prompt}")

    # Generate the image
    try:
        print(f"Generating image with orientation: {orientation}, video quality: {video_quality}")
        return image_generator_func(image_prompt, orientation=orientation, video_quality=video_quality)
    except Exception as e:
        print(f"Error generating image for custom script: {e}")
        return None
