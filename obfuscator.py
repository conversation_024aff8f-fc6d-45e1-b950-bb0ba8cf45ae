#!/usr/bin/env python3
"""
Simple Python file obfuscator for build process.
Uses base64 encoding and zlib compression for basic obfuscation.
Falls back to copying original files if obfuscation fails.
"""

import sys
import os
import shutil
import base64
import zlib


def obfuscate_file(input_file, output_file):
    """
    Simple obfuscation using base64 and zlib compression.

    Args:
        input_file (str): Path to the input Python file
        output_file (str): Path to the output obfuscated file

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Read the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Simple obfuscation: compress and encode
        compressed = zlib.compress(content.encode('utf-8'))
        encoded = base64.b64encode(compressed).decode('ascii')

        # Create obfuscated wrapper that will execute the original code
        wrapper = f"""import base64, zlib; exec(zlib.decompress(base64.b64decode('{encoded}')).decode('utf-8'))"""

        # Ensure output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir:  # Only create directory if there is one
            os.makedirs(output_dir, exist_ok=True)

        # Write the obfuscated file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(wrapper)

        return True

    except Exception as e:
        print(f"Obfuscation failed: {e}")
        # Fallback: copy original file
        try:
            output_dir = os.path.dirname(output_file)
            if output_dir:  # Only create directory if there is one
                os.makedirs(output_dir, exist_ok=True)
            shutil.copy2(input_file, output_file)
            return True
        except Exception as copy_error:
            print(f"Copy fallback failed: {copy_error}")
            return False


def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) == 2 and sys.argv[1] == "--help":
        print("Usage: python obfuscator.py input_file output_file")
        print("Simple Python file obfuscator using base64 and zlib compression.")
        print("Falls back to copying original file if obfuscation fails.")
        sys.exit(0)
    elif len(sys.argv) != 3:
        print("Usage: python obfuscator.py input_file output_file")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist.")
        sys.exit(1)

    # Process the file
    if obfuscate_file(input_file, output_file):
        print(f"Successfully processed {input_file} to {output_file}")
    else:
        print(f"Failed to process {input_file}")
        sys.exit(1)


if __name__ == "__main__":
    main()
