import sys
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, Q<PERSON>abel, QVBoxLayout

def main():
    app = QApplication(sys.argv)
    window = QWidget()
    window.setWindowTitle("PyQt6 Test")
    window.setGeometry(100, 100, 400, 200)
    
    layout = QVBoxLayout()
    label = QLabel("This is a PyQt6 test application.")
    layout.addWidget(label)
    
    window.setLayout(layout)
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 