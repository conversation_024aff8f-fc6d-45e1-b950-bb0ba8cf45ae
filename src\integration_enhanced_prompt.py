"""
Integration module for the enhanced image prompt generator.

This module provides functions to integrate the enhanced image prompt generator
with the main application.
"""

import os
import sys
from typing import Dict, Any, Optional, List, Callable

# Import the enhanced image prompt generator
from enhanced_image_prompt_generator import enhance_image_prompt

def generate_enhanced_prompt(
    description: str,
    subtitles: str = "",
    story_type: str = "general",
    image_style: str = "cinematic",
    is_custom_script: bool = False,
    is_custom_title: bool = False
) -> str:
    """
    Generate an enhanced image prompt using the advanced prompt generator.

    Args:
        description: The scene description
        subtitles: The subtitles for the scene (optional)
        story_type: The type of story
        image_style: The desired image style
        is_custom_script: Whether the text is from a custom script
        is_custom_title: Whether the text is a custom title

    Returns:
        Enhanced image prompt
    """
    # Combine description and subtitles if both are provided
    if subtitles and not is_custom_title:
        combined_text = f"{description} {subtitles}"
    else:
        combined_text = description

    # Generate the enhanced prompt
    return enhance_image_prompt(
        text=combined_text,
        story_type=story_type,
        image_style=image_style,
        is_custom_script=is_custom_script,
        is_custom_title=is_custom_title
    )

def generate_enhanced_image_for_scene(
    scene: Dict[str, Any],
    story_type: str,
    image_style: str,
    image_generator_func: Callable[[str, str, str], Optional[bytes]],
    orientation: str = "portrait",
    video_quality: str = "720p"
) -> Optional[bytes]:
    """
    Generate an image for a scene using the enhanced prompt generator.

    Args:
        scene: The scene data
        story_type: Type of story
        image_style: Style for the image
        image_generator_func: Function to generate images
        orientation: Orientation of the video (portrait, landscape)
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)

    Returns:
        Image bytes if successful, None otherwise
    """
    # Extract scene description and subtitles
    description = scene.get('description', '')
    subtitles = scene.get('subtitles', '')

    # Generate enhanced prompt
    enhanced_prompt = generate_enhanced_prompt(
        description=description,
        subtitles=subtitles,
        story_type=story_type,
        image_style=image_style
    )

    # Generate image using the provided function
    try:
        return image_generator_func(enhanced_prompt, orientation=orientation, video_quality=video_quality)
    except Exception as e:
        print(f"Error generating image: {e}")
        return None

def generate_enhanced_image_for_custom_script(
    paragraph: str,
    story_type: str,
    image_style: str,
    image_generator_func: Callable[[str, str, str], Optional[bytes]],
    orientation: str = "portrait",
    video_quality: str = "720p"
) -> Optional[bytes]:
    """
    Generate an image for a custom script paragraph using the enhanced prompt generator.

    Args:
        paragraph: The paragraph text
        story_type: Type of story
        image_style: Style for the image
        image_generator_func: Function to generate images
        orientation: Orientation of the video (portrait, landscape)
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)

    Returns:
        Image bytes if successful, None otherwise
    """
    # Generate enhanced prompt
    enhanced_prompt = generate_enhanced_prompt(
        description=paragraph,
        story_type=story_type,
        image_style=image_style,
        is_custom_script=True
    )

    # Generate image using the provided function
    try:
        return image_generator_func(enhanced_prompt, orientation=orientation, video_quality=video_quality)
    except Exception as e:
        print(f"Error generating image for custom script: {e}")
        return None

def generate_enhanced_image_for_custom_title(
    title: str,
    story_type: str,
    image_style: str,
    image_generator_func: Callable[[str, str, str], Optional[bytes]],
    orientation: str = "portrait",
    video_quality: str = "720p"
) -> Optional[bytes]:
    """
    Generate an image for a custom title using the enhanced prompt generator.

    Args:
        title: The custom title
        story_type: Type of story
        image_style: Style for the image
        image_generator_func: Function to generate images
        orientation: Orientation of the video (portrait, landscape)
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)

    Returns:
        Image bytes if successful, None otherwise
    """
    # Generate enhanced prompt
    enhanced_prompt = generate_enhanced_prompt(
        description=title,
        story_type=story_type,
        image_style=image_style,
        is_custom_title=True
    )

    # Generate image using the provided function
    try:
        return image_generator_func(enhanced_prompt, orientation=orientation, video_quality=video_quality)
    except Exception as e:
        print(f"Error generating image for custom title: {e}")
        return None

def generate_and_download_enhanced_images(
    storyboard_project: Dict[str, Any],
    story_dir: str,
    image_style: str,
    story_type: str,
    image_generator_func: Callable[[str, str, str], Optional[bytes]],
    video_quality: str = "720p",
    orientation: str = "portrait",
    check_pause_func=None,
    check_stop_func=None
) -> List[str]:
    """
    Generate and download images for each scene in the storyboard using enhanced prompts.

    Args:
        storyboard_project: The storyboard project data
        story_dir: Directory to save images
        image_style: Style for the images
        story_type: Type of story
        image_generator_func: Function to generate images
        video_quality: Quality of the video (720p, 1080p, 2K, 4K)
        orientation: Orientation of the video (portrait, landscape)
        check_pause_func: Optional function to check if generation should pause
        check_stop_func: Optional function to check if generation should stop

    Returns:
        List of image file paths
    """
    # Create directory for images if it doesn't exist
    os.makedirs(story_dir, exist_ok=True)

    image_files = []
    image_prompts = []

    # Get storyboards from the project
    storyboards = storyboard_project.get('storyboards', [])

    # Process each scene
    for i, scene in enumerate(storyboards):
        # Check if we should stop
        if check_stop_func and check_stop_func():
            print("Image generation stopped by user")
            return image_files

        # Check if we should pause
        if check_pause_func:
            check_pause_func()

        scene_number = i + 1
        safe_scene_num = f"{scene_number:02d}"

        # Define image filename with safe scene number
        image_filename = os.path.join(story_dir, f"scene_{safe_scene_num}.png")

        # Extract scene description and subtitles
        description = scene.get('description', '')
        subtitles = scene.get('subtitles', '')

        # Generate enhanced prompt
        enhanced_prompt = generate_enhanced_prompt(
            description=description,
            subtitles=subtitles,
            story_type=story_type,
            image_style=image_style
        )

        # Save the prompt for debugging
        image_prompts.append(enhanced_prompt)
        print(f"Enhanced image prompt for scene {scene_number}: {enhanced_prompt}")

        # Try to generate image with the specified generator
        try:
            print(f"Generating image for scene {scene_number}")

            # Check again if we should stop before making the API call
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return image_files

            # Pass video quality and orientation to the image generator
            image_bytes = image_generator_func(enhanced_prompt, orientation=orientation, video_quality=video_quality)

            # Check again if we should stop after the API call
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return image_files

            if image_bytes:
                with open(image_filename, "wb") as f:
                    f.write(image_bytes)
                print(f"Image saved to {image_filename}")
                image_files.append(image_filename)
            else:
                # If primary generator fails, try the replicate_flux_api as fallback
                from api import replicate_flux_api

                # Check again if we should stop before trying the fallback
                if check_stop_func and check_stop_func():
                    print("Image generation stopped by user")
                    return image_files

                if image_generator_func.__name__ != "replicate_flux_api":
                    print(f"Primary generator failed, trying replicate_flux_api as fallback")

                    # Check if we should pause before the fallback
                    if check_pause_func:
                        check_pause_func()

                    image_bytes = replicate_flux_api(enhanced_prompt, orientation=orientation, video_quality=video_quality)

                    # Check again if we should stop after the fallback API call
                    if check_stop_func and check_stop_func():
                        print("Image generation stopped by user")
                        return image_files

                    if image_bytes:
                        with open(image_filename, "wb") as f:
                            f.write(image_bytes)
                        print(f"Image saved to {image_filename} (using fallback)")
                        image_files.append(image_filename)
                    else:
                        print(f"Fallback generator also failed, creating blank image")
                        from utils import create_blank_image
                        create_blank_image(image_filename)
                        image_files.append(image_filename)
                else:
                    print(f"Primary generator failed, creating blank image")
                    from utils import create_blank_image
                    create_blank_image(image_filename)
                    image_files.append(image_filename)
        except Exception as e:
            print(f"Error generating image for scene {scene_number}: {e}")
            # Create a blank image as fallback
            from utils import create_blank_image
            create_blank_image(image_filename)
            image_files.append(image_filename)

    return image_files
