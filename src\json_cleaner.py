import re
import logging

def clean_json_string(json_str):
    """
    Clean a JSON string to fix common formatting issues that cause parsing errors.

    Args:
        json_str (str): The JSON string to clean

    Returns:
        str: The cleaned JSON string
    """
    # Log the original JSON string (truncated for brevity)
    logging.debug(f"Original JSON string: {json_str[:100]}...")

    # Find the first opening brace and the last closing brace
    start_idx = json_str.find('{')
    end_idx = json_str.rfind('}')

    # Extract only the JSON part if both braces are found
    if start_idx >= 0 and end_idx >= 0 and end_idx > start_idx:
        json_str = json_str[start_idx:end_idx + 1]
        logging.debug(f"Extracted JSON between braces: {json_str[:100]}...")

    # Replace special quotes with regular quotes (both Unicode and actual characters)
    # Unicode quotes
    json_str = json_str.replace('\u201c', '\"').replace('\u201d', '\"')  # Double quotes
    json_str = json_str.replace('\u2018', "'").replace('\u2019', "'")    # Single quotes

    # Actual special quote characters
    json_str = json_str.replace('"', '\"').replace('"', '\"')  # Double quotes
    json_str = json_str.replace(''', "'").replace(''', "'")    # Single quotes

    # Replace single quotes with double quotes for property names
    json_str = re.sub(r"'([^']*)'\s*:", r'"\1":', json_str)

    # Fix trailing commas in arrays and objects
    json_str = re.sub(r',\s*}', '}', json_str)
    json_str = re.sub(r',\s*\]', ']', json_str)

    # Fix missing quotes around property names
    json_str = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_str)

    # Fix missing commas between properties
    json_str = re.sub(r'"([^"]+)":\s*"([^"]*)"\s*"([^"]+)":', r'"\1": "\2","\3":', json_str)

    # Fix specific field issues
    field_patterns = [
        (r'"description":\s*"([^"]*)"\s*"subtitles"', r'"description": "\1","subtitles"'),
        (r'"scene_number":\s*"([^"]*)"\s*"description"', r'"scene_number": "\1","description"'),
        (r'"scene_number":\s*(\d+)\s*"description"', r'"scene_number": \1,"description"'),
        (r'"reading":\s*"([^"]*)"\s*"', r'"reading": "\1","'),
        (r'"message":\s*"([^"]*)"\s*"', r'"message": "\1","'),
        (r'"title":\s*"([^"]*)"\s*"user"', r'"title": "\1","user"'),
    ]

    for pattern, replacement in field_patterns:
        json_str = re.sub(pattern, replacement, json_str)

    # Handle nested quotes in strings by escaping them
    # This is a complex problem, so we'll use a simplified approach
    # Look for unescaped quotes within string values
    def fix_nested_quotes(match):
        # Get the property name and value
        prop_name = match.group(1)
        prop_value = match.group(2)

        # Escape any unescaped quotes in the value
        prop_value = prop_value.replace('\"', '\\"')

        # Return the fixed property
        return f'"{prop_name}": "{prop_value}"'

    # Apply the fix for nested quotes
    json_str = re.sub(r'"([^"]+)":\s*"([^"]*)"', fix_nested_quotes, json_str)

    # Log the cleaned JSON string (truncated for brevity)
    logging.debug(f"Cleaned JSON string: {json_str[:100]}...")

    return json_str
