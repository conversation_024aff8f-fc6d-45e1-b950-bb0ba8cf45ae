"""
Subtitle Generator module for adding customized subtitles to existing videos.
This module provides a dedicated UI for selecting videos and customizing subtitle appearance.
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import datetime
import platform
import subprocess
import tempfile
from typing import Callable, Dict, Any, Optional

# Import from the main application
from main import ModernUI, ModernButton, ColorPickerButton, SliderWithValue, DEFAULT_FONT
import ai_client
import v3_captioner
import custom_captioner

class SubtitleGeneratorThread(threading.Thread):
    """Thread for generating subtitles to avoid UI freezing"""
    def __init__(self, video_path: str, font_name: str, font_size: int,
                 font_color: str, outline_color: str, outline_size: int,
                 caption_words: int, caption_position: str, highlight_style: str,
                 highlight_color: str, highlight_bg_color: str, highlight_bg_opacity: float,
                 callback: Callable[[str], None], update_progress: Callable[[int], None]):
        """Initialize the subtitle generator thread"""
        threading.Thread.__init__(self)
        self.video_path = video_path
        self.font_name = font_name
        self.font_size = font_size
        self.font_color = font_color
        self.outline_color = outline_color
        self.outline_size = outline_size
        self.caption_words = caption_words
        self.caption_position = caption_position
        self.highlight_style = highlight_style
        self.highlight_color = highlight_color
        self.highlight_bg_color = highlight_bg_color
        self.highlight_bg_opacity = highlight_bg_opacity
        self.callback = callback
        self.update_progress = update_progress
        self.result = {"success": False, "message": "", "output_path": None}

    def run(self):
        """Run the subtitle generation process"""
        try:
            # Update status
            self.callback("Analyzing video...")
            self.update_progress(10)

            # Create output file path
            output_dir = os.path.dirname(self.video_path)
            base_name = os.path.basename(self.video_path)
            name, ext = os.path.splitext(base_name)
            output_path = os.path.join(output_dir, f"{name}_subtitled{ext}")

            # Update status
            self.callback("Extracting audio and generating transcription...")
            self.update_progress(30)

            # Get script directory for font path
            script_dir = os.path.dirname(os.path.abspath(__file__))
            font_dir = os.path.join(os.path.dirname(script_dir), "font")

            # Find the font file
            font_file_path = None
            for ext in [".ttf", ".otf", ""]:
                potential_path = os.path.join(font_dir, f"{self.font_name}{ext}")
                if os.path.exists(potential_path):
                    font_file_path = potential_path
                    break

            if not font_file_path:
                # Try to find TitanOne as fallback
                for ext in [".ttf", ".otf", ""]:
                    potential_path = os.path.join(font_dir, f"TitanOne{ext}")
                    if os.path.exists(potential_path):
                        font_file_path = potential_path
                        self.callback(f"Font {self.font_name} not found, using TitanOne as fallback")
                        break

            if not font_file_path:
                raise Exception(f"Font {self.font_name} not found and no fallback fonts available")

            # Update status
            self.callback("Generating subtitles...")
            self.update_progress(50)

            # Add subtitles to the video
            try:
                # First try with the v3 captioner (based on version 3.0 which worked well)
                self.callback("Using v3 captioner for reliable captions...")

                # Add captions to the video using the v3 captioner
                v3_captioner.add_captions(
                    video_file=self.video_path,
                    output_file=output_path,
                    font=font_file_path,
                    font_size=self.font_size,
                    font_color=self.font_color,
                    stroke_width=self.outline_size,
                    stroke_color=self.outline_color,
                    highlight_current_word=True,
                    word_highlight_color=self.highlight_color,
                    words_per_caption=self.caption_words,
                    padding=70,
                    position=self.caption_position,
                    animation_style="none",  # Always use none for animation style
                    highlight_style=self.highlight_style,
                    highlight_bg_color=self.highlight_bg_color,
                    highlight_bg_opacity=self.highlight_bg_opacity
                )

                self.update_progress(90)
                self.callback("Finalizing video...")

            except Exception as e:
                self.callback(f"Error with v3 captioner: {str(e)}")
                self.callback("Trying with custom captioner as fallback...")

                # Using custom_captioner as fallback
                custom_captioner.add_captions(
                    video_file=self.video_path,
                    output_file=output_path,
                    font=font_file_path,
                    font_size=self.font_size,
                    font_color=self.font_color,
                    stroke_width=self.outline_size,
                    stroke_color=self.outline_color,
                    highlight_current_word=True,
                    word_highlight_color=self.highlight_color,
                    words_per_caption=self.caption_words,
                    padding=70,
                    position=self.caption_position,
                    animation_style="none",  # Always use none for animation style
                    highlight_style=self.highlight_style,
                    highlight_bg_color=self.highlight_bg_color,
                    highlight_bg_opacity=self.highlight_bg_opacity
                )

                self.update_progress(90)
                self.callback("Finalizing video...")

            # Check if the output file was created
            if not os.path.exists(output_path):
                raise Exception("Failed to create subtitled video")

            # Update progress and status
            self.update_progress(100)
            self.callback("Subtitle generation completed!")

            # Set result
            self.result = {
                "success": True,
                "message": "Subtitle generation completed!",
                "output_path": output_path
            }

        except Exception as e:
            self.callback(f"Error during subtitle generation: {str(e)}")
            self.result = {
                "success": False,
                "message": f"Error: {str(e)}",
                "output_path": None
            }
            import traceback
            traceback.print_exc()

class SubtitleGeneratorPage:
    """Subtitle Generator page for the main application"""
    def __init__(self, parent_app, page_frame):
        """Initialize the subtitle generator page"""
        self.parent_app = parent_app
        self.page_frame = page_frame
        self.subtitle_generator_thread = None

        # Create the UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the subtitle generator UI"""
        # Create a canvas for scrolling
        subtitle_canvas = tk.Canvas(self.page_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.page_frame, orient=tk.VERTICAL, command=subtitle_canvas.yview)

        # Configure the canvas
        subtitle_canvas.configure(yscrollcommand=scrollbar.set)
        subtitle_canvas.bind('<Configure>', lambda e: subtitle_canvas.configure(scrollregion=subtitle_canvas.bbox("all")))

        # Bind mouse wheel events for scrolling
        self.parent_app._bind_mousewheel(subtitle_canvas)

        # Create a frame inside the canvas to hold the content
        subtitle_content = ttk.Frame(subtitle_canvas)

        # Pack the scrollbar and canvas
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        subtitle_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        subtitle_canvas.create_window((0, 0), window=subtitle_content, anchor="nw")

        # Update mouse wheel bindings after content is created
        subtitle_content.bind("<Configure>", lambda e: self.parent_app._bind_mousewheel_to_children(subtitle_content, subtitle_canvas))

        # Header section
        header_frame = ttk.Frame(subtitle_content)
        header_frame.pack(fill=tk.X, padx=30, pady=(30, 20))

        # Title and description
        ttk.Label(
            header_frame,
            text="Subtitle Generator",
            style='Headline.TLabel'
        ).pack(anchor='w')

        ttk.Label(
            header_frame,
            text="Add customized subtitles to your existing videos",
            style='Subtitle.TLabel'
        ).pack(anchor='w', pady=(5, 0))

        # Add limitation note with warning color
        limitation_frame = ttk.Frame(header_frame)
        limitation_frame.pack(anchor='w', pady=(10, 0))

        ttk.Label(
            limitation_frame,
            text="⚠️ Note:",
            foreground="#FF6B00",  # Orange warning color
            font=(DEFAULT_FONT, 11, "bold")
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(
            limitation_frame,
            text="Only English language supported and maximum of 5 minute video supported",
            foreground="#FF6B00",  # Orange warning color
            font=(DEFAULT_FONT, 11)
        ).pack(side=tk.LEFT)

        # Main content area with two columns - adjusted proportions
        main_frame = ttk.Frame(subtitle_content)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Configure grid columns with better proportions - give even more space to customization
        main_frame.columnconfigure(0, weight=1)  # Video selection (smaller)
        main_frame.columnconfigure(1, weight=4)  # Subtitle customization (larger)

        # Left column - Video selection (more compact)
        left_column = ttk.Frame(main_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 15))

        # Video selection card
        video_card = ttk.Frame(left_column, style='Card.TFrame', padding=15)
        video_card.pack(fill=tk.X, pady=10)

        # Card header
        ttk.Label(
            video_card,
            text="Select Video",
            style='Title.TLabel'
        ).pack(anchor='w', pady=(0, 10))

        # Video selection options
        self.video_path_var = tk.StringVar()

        # Upload a video - more compact layout
        upload_frame = ttk.Frame(video_card)
        upload_frame.pack(fill=tk.X, pady=5)

        # Entry to display selected file path
        self.video_path_entry = ttk.Entry(
            upload_frame,
            textvariable=self.video_path_var,
            state='readonly',
            width=30
        )
        self.video_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # Browse button
        browse_button = ModernButton(
            upload_frame,
            text="Browse",
            command=self.browse_video_file,
            type="outlined"
        )
        browse_button.pack(side=tk.RIGHT)

        # Right column - Subtitle customization (larger)
        right_column = ttk.Frame(main_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(15, 0))

        # Subtitle customization card - larger and more prominent
        subtitle_card = ttk.Frame(right_column, style='Card.TFrame', padding=20)
        subtitle_card.pack(fill=tk.BOTH, expand=True, pady=10)

        # Card header with more emphasis
        ttk.Label(
            subtitle_card,
            text="Customize Subtitles",
            style='Title.TLabel',
            font=(DEFAULT_FONT, 14, "bold")
        ).pack(anchor='w', pady=(0, 15))

        # Create a grid for the settings with better spacing
        settings_frame = ttk.Frame(subtitle_card)
        settings_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Configure grid columns for better layout
        settings_frame.columnconfigure(0, weight=1)  # Label column
        settings_frame.columnconfigure(1, weight=2)  # Input column

        # Font selection
        ttk.Label(
            settings_frame,
            text="Font:",
            style='FormField.TLabel'
        ).grid(row=0, column=0, sticky=tk.W, pady=10)

        # Get available fonts
        script_dir = os.path.dirname(os.path.abspath(__file__))
        font_dir = os.path.join(os.path.dirname(script_dir), "font")
        available_fonts = []

        # Create font directory if it doesn't exist
        os.makedirs(font_dir, exist_ok=True)

        # Scan for font files in the font directory
        if os.path.exists(font_dir):
            for file in os.listdir(font_dir):
                if file.lower().endswith(('.ttf', '.otf')):
                    # Use the filename without extension as the display name
                    font_name = os.path.splitext(file)[0]
                    available_fonts.append(font_name)

        # Sort fonts alphabetically
        available_fonts.sort()

        # Default to TitanOne if available
        default_font = "TitanOne" if "TitanOne" in available_fonts else (available_fonts[0] if available_fonts else "Arial")

        self.subtitle_font_var = tk.StringVar(value=default_font)
        self.subtitle_font_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.subtitle_font_var,
            values=available_fonts,
            state="readonly",
            width=25
        )
        self.subtitle_font_combo.grid(row=0, column=1, sticky=tk.W, pady=10, padx=5)

        # Font size
        ttk.Label(
            settings_frame,
            text="Font Size:",
            style='FormField.TLabel'
        ).grid(row=1, column=0, sticky=tk.W, pady=10)

        self.font_size_slider = SliderWithValue(
            settings_frame,
            from_=20,
            to=300,  # Maximum value of 300 as requested
            initial=40,
            command=lambda val: None,
            value_format='pixels'
        )
        self.font_size_slider.grid(row=1, column=1, sticky=tk.W, pady=10, padx=5)

        # Font color
        ttk.Label(
            settings_frame,
            text="Font Color:",
            style='FormField.TLabel'
        ).grid(row=2, column=0, sticky=tk.W, pady=10)

        self.font_color_picker = ColorPickerButton(
            settings_frame,
            initial_color="#FFFFFF",
            command=lambda color: None
        )
        self.font_color_picker.grid(row=2, column=1, sticky=tk.W, pady=10, padx=5)

        # Outline color
        ttk.Label(
            settings_frame,
            text="Outline Color:",
            style='FormField.TLabel'
        ).grid(row=3, column=0, sticky=tk.W, pady=10)

        self.outline_color_picker = ColorPickerButton(
            settings_frame,
            initial_color="#000000",
            command=lambda color: None
        )
        self.outline_color_picker.grid(row=3, column=1, sticky=tk.W, pady=10, padx=5)

        # Outline size
        ttk.Label(
            settings_frame,
            text="Outline Size:",
            style='FormField.TLabel'
        ).grid(row=4, column=0, sticky=tk.W, pady=10)

        self.outline_size_slider = SliderWithValue(
            settings_frame,
            from_=0,
            to=10,
            initial=3,
            command=lambda val: None,
            value_format='pixels'
        )
        self.outline_size_slider.grid(row=4, column=1, sticky=tk.W, pady=10, padx=5)

        # Words per caption
        ttk.Label(
            settings_frame,
            text="Words per Caption:",
            style='FormField.TLabel'
        ).grid(row=5, column=0, sticky=tk.W, pady=10)

        self.words_per_caption_slider = SliderWithValue(
            settings_frame,
            from_=1,
            to=10,
            initial=3,
            command=lambda val: None,
            value_format='words'
        )
        self.words_per_caption_slider.grid(row=5, column=1, sticky=tk.W, pady=10, padx=5)

        # Caption position
        ttk.Label(
            settings_frame,
            text="Caption Position:",
            style='FormField.TLabel'
        ).grid(row=6, column=0, sticky=tk.W, pady=10)

        self.caption_position_var = tk.StringVar(value="bottom")
        self.caption_position_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.caption_position_var,
            values=["top", "center", "bottom"],
            state="readonly",
            width=25
        )
        self.caption_position_combo.grid(row=6, column=1, sticky=tk.W, pady=10, padx=5)

        # Highlight style
        ttk.Label(
            settings_frame,
            text="Highlight Style:",
            style='FormField.TLabel'
        ).grid(row=7, column=0, sticky=tk.W, pady=10)

        self.highlight_style_var = tk.StringVar(value="text_color")
        self.highlight_style_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.highlight_style_var,
            values=["text_color", "background"],
            state="readonly",
            width=25
        )
        self.highlight_style_combo.grid(row=7, column=1, sticky=tk.W, pady=10, padx=5)
        self.highlight_style_combo.bind("<<ComboboxSelected>>", self.on_highlight_style_change)

        # Highlight color
        ttk.Label(
            settings_frame,
            text="Highlight Color:",
            style='FormField.TLabel'
        ).grid(row=8, column=0, sticky=tk.W, pady=10)

        self.highlight_color_picker = ColorPickerButton(
            settings_frame,
            initial_color="#FFFF00",
            command=lambda color: None
        )
        self.highlight_color_picker.grid(row=8, column=1, sticky=tk.W, pady=10, padx=5)

        # Background highlight color (only visible when highlight_style is "background")
        ttk.Label(
            settings_frame,
            text="Background Color:",
            style='FormField.TLabel'
        ).grid(row=9, column=0, sticky=tk.W, pady=10)

        self.highlight_bg_color_picker = ColorPickerButton(
            settings_frame,
            initial_color="#3700B3",
            command=lambda color: None
        )
        self.highlight_bg_color_picker.grid(row=9, column=1, sticky=tk.W, pady=10, padx=5)

        # Background highlight opacity (only visible when highlight_style is "background")
        ttk.Label(
            settings_frame,
            text="Background Opacity:",
            style='FormField.TLabel'
        ).grid(row=10, column=0, sticky=tk.W, pady=10)

        self.highlight_bg_opacity_slider = SliderWithValue(
            settings_frame,
            from_=0.1,
            to=1.0,
            initial=0.7,
            command=lambda val: None,
            value_format='percent'
        )
        self.highlight_bg_opacity_slider.grid(row=10, column=1, sticky=tk.W, pady=10, padx=5)

        # Set initial state of background highlight options based on highlight style
        self.on_highlight_style_change()

        # Bottom section with generate button and progress
        bottom_frame = ttk.Frame(subtitle_content)
        bottom_frame.pack(fill=tk.X, padx=30, pady=(10, 30))

        # Progress bar and status
        progress_frame = ttk.Frame(bottom_frame)
        progress_frame.pack(fill=tk.X, pady=10)

        self.subtitle_progress_var = tk.IntVar(value=0)
        self.subtitle_progress = ttk.Progressbar(
            progress_frame,
            orient=tk.HORIZONTAL,
            length=100,
            mode='determinate',
            variable=self.subtitle_progress_var
        )
        self.subtitle_progress.pack(fill=tk.X, pady=(0, 5))

        self.subtitle_status_var = tk.StringVar(value="Ready to generate subtitles")
        self.subtitle_status = ttk.Label(
            progress_frame,
            textvariable=self.subtitle_status_var,
            style='Status.TLabel'
        )
        self.subtitle_status.pack(anchor='w')

        # Generate button
        button_frame = ttk.Frame(bottom_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.generate_subtitle_button = ModernButton(
            button_frame,
            text="Generate Subtitles",
            command=self.generate_subtitles,
            type="primary"
        )
        self.generate_subtitle_button.pack(side=tk.RIGHT)

        # Output section
        output_frame = ttk.Frame(subtitle_content)
        output_frame.pack(fill=tk.X, padx=30, pady=(0, 30))

        # Output video path
        self.output_video_path_var = tk.StringVar()
        self.output_video_path = ttk.Label(
            output_frame,
            textvariable=self.output_video_path_var,
            style='Body.TLabel'
        )
        self.output_video_path.pack(anchor='w', pady=5)

        # Open video button (initially disabled)
        self.open_subtitle_video_button = ModernButton(
            output_frame,
            text="Open Video",
            command=self.open_subtitle_video,
            type="outlined"
        )
        self.open_subtitle_video_button.pack(side=tk.LEFT, padx=(0, 10))
        self.open_subtitle_video_button.config(state=tk.DISABLED)

        # Open folder button (initially disabled)
        self.open_subtitle_folder_button = ModernButton(
            output_frame,
            text="Open Folder",
            command=self.open_subtitle_folder,
            type="outlined"
        )
        self.open_subtitle_folder_button.pack(side=tk.LEFT)
        self.open_subtitle_folder_button.config(state=tk.DISABLED)

    def browse_video_file(self):
        """Open a file dialog to select a video file"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video Files", "*.mp4 *.avi *.mov *.mkv"), ("All Files", "*.*")]
        )

        if file_path:
            self.video_path_var.set(file_path)

    def on_highlight_style_change(self, event=None):
        """Update UI based on selected highlight style"""
        highlight_style = self.highlight_style_var.get()

        if highlight_style == "background":
            # Enable background highlight options
            self.highlight_bg_color_picker.set_state(tk.NORMAL)
            self.highlight_bg_opacity_slider.set_state(tk.NORMAL)
        else:
            # Disable background highlight options
            self.highlight_bg_color_picker.set_state(tk.DISABLED)
            self.highlight_bg_opacity_slider.set_state(tk.DISABLED)

    def generate_subtitles(self):
        """Generate subtitles for the selected video"""
        # Get the selected video path
        video_path = self.video_path_var.get()

        if not video_path:
            messagebox.showerror("Error", "Please select a video file first")
            return

        if not os.path.exists(video_path):
            messagebox.showerror("Error", "The selected video file does not exist")
            return

        # Check video length (requires moviepy)
        try:
            from moviepy.editor import VideoFileClip
            clip = VideoFileClip(video_path)
            duration_minutes = clip.duration / 60
            clip.close()

            if duration_minutes > 5:
                messagebox.showerror(
                    "Video Too Long",
                    f"The selected video is {duration_minutes:.1f} minutes long. " +
                    "Only videos up to 5 minutes are supported."
                )
                return
        except Exception as e:
            # If we can't check the duration, log the error but continue
            print(f"Could not check video duration: {str(e)}")
            # We'll still try to process the video

        # Disable the generate button
        self.generate_subtitle_button.config(state=tk.DISABLED)
        self.subtitle_progress_var.set(0)
        self.subtitle_status_var.set("Preparing to generate subtitles...")

        # Get subtitle settings
        font_name = self.subtitle_font_var.get()
        font_size = int(self.font_size_slider.get())
        font_color = self.font_color_picker.get_color()
        outline_color = self.outline_color_picker.get_color()
        outline_size = int(self.outline_size_slider.get())
        caption_words = int(self.words_per_caption_slider.get())
        caption_position = self.caption_position_var.get()
        highlight_style = self.highlight_style_var.get()
        highlight_color = self.highlight_color_picker.get_color()
        highlight_bg_color = self.highlight_bg_color_picker.get_color()
        highlight_bg_opacity = self.highlight_bg_opacity_slider.get()

        # Create and start the subtitle generator thread
        self.subtitle_generator_thread = SubtitleGeneratorThread(
            video_path,
            font_name,
            font_size,
            font_color,
            outline_color,
            outline_size,
            caption_words,
            caption_position,
            highlight_style,
            highlight_color,
            highlight_bg_color,
            highlight_bg_opacity,
            self.update_subtitle_status,
            self.update_subtitle_progress
        )
        self.subtitle_generator_thread.start()

        # Start checking the thread status
        self.parent_app.root.after(100, self.check_subtitle_thread)

    def update_subtitle_status(self, message):
        """Update the subtitle generation status message"""
        self.subtitle_status_var.set(message)
        self.parent_app.root.update_idletasks()

    def update_subtitle_progress(self, progress):
        """Update the subtitle generation progress bar"""
        self.subtitle_progress_var.set(progress)
        self.parent_app.root.update_idletasks()

    def check_subtitle_thread(self):
        """Check if the subtitle generator thread has completed"""
        if self.subtitle_generator_thread and not self.subtitle_generator_thread.is_alive():
            result = self.subtitle_generator_thread.result
            if result["success"]:
                # Ensure progress bar is at 100%
                self.subtitle_progress_var.set(100)
                self.update_subtitle_status("Subtitle generation completed!")
                self.output_video_path_var.set(f"Output video: {result['output_path']}")
                self.open_subtitle_video_button.config(state=tk.NORMAL)
                self.open_subtitle_folder_button.config(state=tk.NORMAL)
            else:
                self.update_subtitle_status(f"Error: {result['message']}")

            # Re-enable the generate button
            self.generate_subtitle_button.config(state=tk.NORMAL)
        else:
            # Check again after 100ms
            self.parent_app.root.after(100, self.check_subtitle_thread)

    def open_subtitle_video(self):
        """Open the generated subtitled video"""
        output_path = self.subtitle_generator_thread.result.get("output_path") if self.subtitle_generator_thread else None

        if output_path and os.path.exists(output_path):
            if platform.system() == "Windows":
                os.startfile(output_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", output_path])
            else:  # Linux
                subprocess.Popen(["xdg-open", output_path])
        else:
            messagebox.showerror("Error", "Video file not found")

    def open_subtitle_folder(self):
        """Open the folder containing the generated subtitled video"""
        output_path = self.subtitle_generator_thread.result.get("output_path") if self.subtitle_generator_thread else None

        if output_path and os.path.exists(output_path):
            folder_path = os.path.dirname(output_path)
            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", folder_path])
            else:  # Linux
                subprocess.Popen(["xdg-open", folder_path])
        else:
            messagebox.showerror("Error", "Folder not found")
