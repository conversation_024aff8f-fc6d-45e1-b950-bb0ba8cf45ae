"""
1ClickVideo by Galib GUI

A simple graphical user interface for the 1ClickVideo by Galib tool.
"""

import os
import sys
import threading
import subprocess
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox, colorchooser
import time
import json

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

try:
    import customtkinter as ctk
except ImportError:
    messagebox.showerror("Import Error", "The customtkinter module is required for this GUI.\n\nPlease install it with: pip install customtkinter")
    sys.exit(1)

# Import main video generation functionality
src_path = str(Path(__file__).parent / "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# Import with try-catch to handle import issues gracefully
try:
    from main import VideoGeneratorThread, switch_ai_provider, process_custom_script, process_custom_title
    import ai_client
    from utils import pick_story_type, pick_image_style
    from api import replicate_flux_api, fal_flux_api, together_flux_api
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"Warning: Could not import main functionality: {e}")
    print("The GUI will run in limited mode.")
    IMPORTS_SUCCESSFUL = False
    # Create placeholder classes/functions
    class VideoGeneratorThread:
        def __init__(self, *args, **kwargs):
            pass
        def start(self):
            pass
        def is_alive(self):
            return False

    class ai_client:
        @staticmethod
        def initialize_clients():
            pass

# Set appearance mode and theme
ctk.set_appearance_mode("System")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

# Define color constants
VALID_API_COLOR = "#3FBD49"    # Green for valid API keys
INVALID_API_COLOR = "#F30000"  # Red for invalid or disabled APIs

class OneClickVideoGUI(ctk.CTk):
    """Main GUI class for 1ClickVideo by Galib tool"""

    def __init__(self):
        super().__init__()

        # Configure window
        self.title("1ClickVideo by Galib")

        # Set window size and position - center on screen
        window_width = 1200
        window_height = 700

        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # Calculate position to center the window
        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)

        # Set geometry (width x height + x_offset + y_offset)
        self.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

        # Set minimum size
        self.minsize(900, 600)

        # Prevent window from opening maximized
        self.state('normal')
        self.resizable(True, True)

        # Create variables
        self.topic_var = tk.StringVar()
        self.custom_script_var = tk.StringVar()
        self.script_file_var = tk.StringVar()
        self.duration_var = tk.StringVar(value="60")
        self.scenes_var = tk.StringVar(value="8")
        self.style_var = tk.StringVar(value="realistic")
        self.transition_var = tk.StringVar(value="fade")
        self.aspect_var = tk.StringVar(value="16:9")
        self.use_dynamic_var = tk.BooleanVar(value=True)

        # New variables for Content Options and Voice Options
        self.story_type_var = tk.StringVar(value="Fun Facts")
        self.image_generator_var = tk.StringVar(value="Together AI Flux")
        self.ai_provider_var = tk.StringVar(value="OpenAI")
        self.tts_model_var = tk.StringVar(value="Voicely")
        self.filter_locale_var = tk.StringVar(value="All")
        self.voice_var = tk.StringVar(value="NatashaNeutral")
        self.speech_rate_var = tk.DoubleVar(value=100)
        self.subtitle_font_var = tk.StringVar(value="TitanOne")

        # Add image style variable for comprehensive style selection
        self.image_style_var = tk.StringVar(value="cinematic")

        # New variables for Video Settings
        self.background_music_var = tk.StringVar(value="None")
        self.music_volume_var = tk.IntVar(value=20)
        self.end_pause_duration_var = tk.DoubleVar(value=3.5)
        self.video_quality_var = tk.StringVar(value="720p")
        self.effects_var = tk.StringVar(value="None")  # New variable for effects dropdown

        # New variables for Subtitle Settings
        self.font_color_var = tk.StringVar(value="#FFFFFF")
        self.font_size_var = tk.IntVar(value=50)
        self.outline_color_var = tk.StringVar(value="#000000")
        self.outline_size_var = tk.IntVar(value=3)
        self.caption_position_var = tk.StringVar(value="Bottom")
        self.words_per_caption_var = tk.StringVar(value="5")
        self.word_highlighting_var = tk.BooleanVar(value=True)
        self.highlight_color_var = tk.StringVar(value="#FFFF00")
        self.highlight_style_var = tk.StringVar(value="Text Color")
        self.enable_subtitles_var = tk.BooleanVar(value=True)  # Enable subtitles by default

        # New variables for Script Options
        self.use_custom_title_var = tk.BooleanVar(value=False)
        self.custom_title_var = tk.StringVar(value="")
        self.use_custom_script_var = tk.BooleanVar(value=False)

        # New variables for Batch Generation
        self.enable_batch_var = tk.BooleanVar(value=False)
        self.batch_count_var = tk.StringVar(value="5")
        self.use_custom_titles_file_var = tk.BooleanVar(value=False)
        self.custom_titles_file_var = tk.StringVar(value="")

        # Generation Control variables
        self.is_generating_var = tk.BooleanVar(value=False)
        self.current_generation_process = None
        self.current_generation_thread = None  # Track the VideoGeneratorThread

        # For tracking the recent video path
        self.recent_video_var = tk.StringVar(value="No videos generated yet")
        self.last_generated_video_path = None  # Track the full path to the last generated video

        # API selection variables
        self.script_api_var = tk.StringVar(value="OpenAI")
        self.image_api_var = tk.StringVar(value="Together AI Flux")
        self.voice_api_var = tk.StringVar(value="OpenAI")

        # Setup variable traces for API selections to update dropdown colors
        self.script_api_var.trace_add("write", self._update_api_colors)
        self.image_api_var.trace_add("write", self._update_api_colors)
        self.voice_api_var.trace_add("write", self._update_api_colors)

        # Add trace to voice_api_var to update voice settings state
        self.voice_api_var.trace_add("write", self._toggle_voice_settings)

        # Input type (mutually exclusive)
        self.input_type_var = tk.StringVar(value="topic")

        # Add trace to input_type_var to toggle duration field when custom script is selected
        self.input_type_var.trace_add("write", self._toggle_duration_field)

        # Add trace to enable_batch_var to manage duration field
        self.enable_batch_var.trace_add("write", self._toggle_duration_field)

        # Profiles system
        self.profiles = {}
        self.current_profile_var = tk.StringVar(value="Default")

        # API key manager for validation (placeholder)
        self.key_manager = None

        # Initialize AI client if imports were successful
        if IMPORTS_SUCCESSFUL:
            ai_client.initialize_clients()

        # Create the UI
        self._create_ui()

        # Load saved profiles
        self._load_profiles_from_file()

        # Update API colors initially
        self.after(100, self._update_api_colors)

    def _create_ui(self):
        """Create the user interface"""
        # Create main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ctk.CTkLabel(main_frame, text="1ClickVideo by Galib", font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=10)

        # Create horizontal layout with two columns
        horizontal_frame = ctk.CTkFrame(main_frame)
        horizontal_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Left column - contains main options (much wider than right column)
        left_column = ctk.CTkFrame(horizontal_frame, width=550)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5), pady=5)
        left_column.pack_propagate(False)  # Don't let the contents determine the size

        # Right column - contains additional settings and generation controls (expanded)
        right_column = ctk.CTkFrame(horizontal_frame)
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)

        # Use regular frame for left column
        left_frame = left_column

        # Create scrollable frame for right column to handle overflow
        right_scrollable = ctk.CTkScrollableFrame(right_column)
        right_scrollable.pack(fill=tk.BOTH, expand=True)

        # API Selection Frame - Top of left column
        api_frame = ctk.CTkFrame(left_frame)
        api_frame.pack(fill=tk.X, padx=10, pady=5)

        api_label = ctk.CTkLabel(api_frame, text="API Selection", font=ctk.CTkFont(weight="bold"))
        api_label.pack(anchor="w", padx=10, pady=5)

        # API selection grid
        api_grid = ctk.CTkFrame(api_frame)
        api_grid.pack(fill=tk.X, padx=10, pady=5)

        # LLM Provider (Script API)
        script_api_label = ctk.CTkLabel(api_grid, text="LLM Provider:")
        script_api_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        script_api_options = ["OpenAI", "Groq"]
        self.script_api_dropdown = ctk.CTkOptionMenu(api_grid, variable=self.script_api_var, values=script_api_options)
        self.script_api_dropdown.grid(row=0, column=1, padx=10, pady=5)

        # Image Model
        image_api_label = ctk.CTkLabel(api_grid, text="Image Model:")
        image_api_label.grid(row=0, column=2, padx=10, pady=5, sticky="w")

        image_api_options = ["Together AI Flux", "Replicate Flux", "FAL AI Flux"]
        self.image_api_dropdown = ctk.CTkOptionMenu(api_grid, variable=self.image_api_var, values=image_api_options)
        self.image_api_dropdown.grid(row=0, column=3, padx=10, pady=5)

        # TTS Model
        voice_api_label = ctk.CTkLabel(api_grid, text="TTS Model:")
        voice_api_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        voice_api_options = ["OpenAI", "Voicely", "ElevenLabs"]
        self.voice_api_dropdown = ctk.CTkOptionMenu(api_grid, variable=self.voice_api_var, values=voice_api_options, command=self._on_tts_model_change)
        self.voice_api_dropdown.grid(row=1, column=1, padx=10, pady=5)

        # API Settings button (placeholder)
        api_status_button = ctk.CTkButton(api_grid, text="API Settings", command=self._show_api_settings)
        api_status_button.grid(row=1, column=3, padx=10, pady=5, sticky="e")

        # Input selection frame - Right after API Selection
        input_frame = ctk.CTkFrame(left_frame)
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        input_label = ctk.CTkLabel(input_frame, text="Input Type:", font=ctk.CTkFont(weight="bold"))
        input_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")

        # Input type radio buttons
        topic_radio = ctk.CTkRadioButton(input_frame, text="Topic", variable=self.input_type_var, value="topic",
                                         command=self._update_input_frame)
        topic_radio.grid(row=0, column=1, padx=10, pady=10)

        custom_radio = ctk.CTkRadioButton(input_frame, text="Custom Script", variable=self.input_type_var, value="custom",
                                          command=self._update_input_frame)
        custom_radio.grid(row=0, column=2, padx=10, pady=10)

        file_radio = ctk.CTkRadioButton(input_frame, text="Script File", variable=self.input_type_var, value="file",
                                        command=self._update_input_frame)
        file_radio.grid(row=0, column=3, padx=10, pady=10)

        # Input content frame (changes based on selection)
        self.content_frame = ctk.CTkFrame(left_frame)
        self.content_frame.pack(fill=tk.X, padx=10, pady=5)
        self.content_frame.configure(height=200)  # Set fixed height
        self.content_frame.pack_propagate(False)  # Prevent resizing based on content

        # Story Type Selection Frame - Add after input content frame
        story_type_frame = ctk.CTkFrame(left_frame)
        story_type_frame.pack(fill=tk.X, padx=10, pady=5)

        story_type_label = ctk.CTkLabel(story_type_frame, text="Story Type", font=ctk.CTkFont(weight="bold"))
        story_type_label.pack(anchor="w", padx=10, pady=5)

        story_type_grid = ctk.CTkFrame(story_type_frame)
        story_type_grid.pack(fill=tk.X, padx=10, pady=5)

        # Comprehensive story types from main.py
        story_type_options = [
            "Scary", "Mystery", "Bedtime", "Interesting History", "Urban Legends",
            "Motivational", "Fun Facts", "Long Form Jokes", "Life Pro Tips", "Philosophy",
            "Love", "AITA Stories", "Storytime", "POV", "Day in the Life",
            "True Crime", "Celebrity Facts", "Conspiracy Theories", "Money Saving Tips",
            "Fitness Hacks", "Psychology Facts", "Product Reviews", "Travel Guides",
            "DIY Tutorials", "Cooking Tips", "Dating Advice", "Pet Tips", "Islamic"
        ]

        story_type_dropdown = ctk.CTkOptionMenu(story_type_grid, variable=self.story_type_var, values=story_type_options)
        story_type_dropdown.pack(fill=tk.X, padx=10, pady=5)

        # Video Settings Frame - First in right column
        video_settings_frame = ctk.CTkFrame(right_scrollable)
        video_settings_frame.pack(fill=tk.X, padx=10, pady=5)

        video_settings_label = ctk.CTkLabel(video_settings_frame, text="Video Settings", font=ctk.CTkFont(weight="bold"))
        video_settings_label.pack(anchor="w", padx=10, pady=5)

        video_settings_grid = ctk.CTkFrame(video_settings_frame)
        video_settings_grid.pack(fill=tk.X, padx=10, pady=5)

        # Aspect Ratio (dropdown style like video quality)
        aspect_label = ctk.CTkLabel(video_settings_grid, text="Aspect Ratio:")
        aspect_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        aspect_options = ["Portrait (9:16)", "Landscape (16:9)", "Square (1:1)"]
        self.aspect_dropdown = ctk.CTkOptionMenu(video_settings_grid, values=aspect_options,
                                               command=self._set_aspect_ratio)
        self.aspect_dropdown.grid(row=0, column=1, padx=10, pady=5)
        # Set initial value based on self.aspect_var
        aspect_text = self._get_aspect_display(self.aspect_var.get())
        self.aspect_dropdown.set(aspect_text)

        # Background Music - Enhanced with actual music file scanning
        bg_music_label = ctk.CTkLabel(video_settings_grid, text="Background Music:")
        bg_music_label.grid(row=0, column=2, padx=(80, 10), pady=5, sticky="w")

        # Get available music files from the music directory (like main.py)
        music_options = self._get_available_music_files()
        self.bg_music_dropdown = ctk.CTkOptionMenu(video_settings_grid, variable=self.background_music_var, values=music_options)
        self.bg_music_dropdown.grid(row=0, column=3, padx=10, pady=5)

        # Video Quality
        video_quality_label = ctk.CTkLabel(video_settings_grid, text="Video Quality:")
        video_quality_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        video_quality_options = ["720p", "1080p", "2K", "4K"]
        video_quality_dropdown = ctk.CTkOptionMenu(video_settings_grid, variable=self.video_quality_var, values=video_quality_options)
        video_quality_dropdown.grid(row=1, column=1, padx=10, pady=5)

        # Music Volume - Below Background Music
        music_volume_label = ctk.CTkLabel(video_settings_grid, text="Music Volume:")
        music_volume_label.grid(row=1, column=2, padx=(80, 5), pady=5, sticky="w")

        music_volume_slider = ctk.CTkSlider(video_settings_grid, from_=0, to=100, variable=self.music_volume_var)
        music_volume_slider.grid(row=1, column=3, padx=(5, 5), pady=5, sticky="ew")

        # Ensure the percentage symbol is displayed and always visible
        music_volume_value_label = ctk.CTkLabel(video_settings_grid, text="20%", width=40)
        music_volume_value_label.grid(row=1, column=4, padx=(0, 5), pady=5, sticky="w")
        # Update label when slider changes - ensure % is shown
        music_volume_slider.configure(command=lambda val: music_volume_value_label.configure(text=f"{int(val)}%"))

        # Video Quality info - Below Video Quality
        video_quality_info = ctk.CTkLabel(video_settings_grid, text="Higher quality requires more processing power",
                                          font=ctk.CTkFont(size=10), text_color="silver")
        video_quality_info.grid(row=2, column=0, columnspan=2, padx=10, pady=(0, 5), sticky="w")

        # End Pause Duration - Below Music Volume
        end_pause_label = ctk.CTkLabel(video_settings_grid, text="End Pause Duration (s):")
        end_pause_label.grid(row=2, column=2, padx=(80, 5), pady=5, sticky="w")

        end_pause_slider = ctk.CTkSlider(video_settings_grid, from_=0, to=10, variable=self.end_pause_duration_var)
        end_pause_slider.grid(row=2, column=3, padx=(5, 5), pady=5, sticky="ew")

        # Ensure the seconds text is displayed and always visible
        end_pause_value_label = ctk.CTkLabel(video_settings_grid, text="3.5 seconds", width=70)
        end_pause_value_label.grid(row=2, column=4, padx=(0, 5), pady=5, sticky="w")
        # Update label when slider changes - ensure "seconds" is shown
        end_pause_slider.configure(command=lambda val: end_pause_value_label.configure(text=f"{val:.1f} seconds"))

        # Duration and Scenes removed as not needed in main.py

        # Style - Updated with comprehensive image styles from main.py
        style_label = ctk.CTkLabel(video_settings_grid, text="Image Style:")
        style_label.grid(row=4, column=0, padx=10, pady=5, sticky="w")
        style_options = [
            "photorealistic", "cinematic", "anime", "comic-book", "pixar-art",
            "dark-aesthetic", "neon-cyberpunk", "minimalist", "film-noir",
            "retro-80s", "vaporwave", "cottagecore", "hyperrealistic",
            "flat-design", "3d-cartoon", "pastel-dreamscape", "fantasy-vibrant",
            "nostalgic-filter", "vhs-aesthetic", "y2k", "god-anime-vine", "ghibli"
        ]
        style_dropdown = ctk.CTkOptionMenu(video_settings_grid, variable=self.image_style_var, values=style_options)
        style_dropdown.grid(row=4, column=1, padx=10, pady=5)

        # Effects removed as not needed in main.py

        # Voice Options Frame - Next in right column
        voice_options_frame = ctk.CTkFrame(right_scrollable)
        voice_options_frame.pack(fill=tk.X, padx=10, pady=5)

        voice_options_label = ctk.CTkLabel(voice_options_frame, text="Voice Settings", font=ctk.CTkFont(weight="bold"))
        voice_options_label.pack(anchor="w", padx=10, pady=5)

        voice_options_grid = ctk.CTkFrame(voice_options_frame)
        voice_options_grid.pack(fill=tk.X, padx=10, pady=5)

        # Voice selection
        self.voice_label = ctk.CTkLabel(voice_options_grid, text="Voice:")
        self.voice_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        # Initialize with OpenAI voices (default)
        voice_options = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
        self.voice_dropdown = ctk.CTkOptionMenu(voice_options_grid, variable=self.voice_var, values=voice_options)
        self.voice_dropdown.grid(row=0, column=1, padx=(10, 0), pady=5)
        self.voice_var.set("alloy")  # Set default OpenAI voice

        # Preview Voice button - moved to be beside voice dropdown with enhanced functionality
        self.preview_voice_button = ctk.CTkButton(voice_options_grid, text="▶", width=30, command=self._preview_voice_enhanced)
        self.preview_voice_button.grid(row=0, column=1, padx=(210, 5), pady=5)

        # Filter by Location
        filter_locale_label = ctk.CTkLabel(voice_options_grid, text="Filter by Location:")
        self.filter_locale_label = filter_locale_label
        filter_locale_label.grid(row=0, column=2, padx=(100, 10), pady=5, sticky="w")

        filter_locale_options = ["All", "en-US", "en-GB", "es-ES", "fr-FR", "de-DE", "it-IT", "pt-BR", "ja-JP", "ko-KR"]
        filter_locale_dropdown = ctk.CTkOptionMenu(voice_options_grid, variable=self.filter_locale_var, values=filter_locale_options, command=self._on_locale_change)
        self.filter_locale_dropdown = filter_locale_dropdown
        filter_locale_dropdown.grid(row=0, column=3, padx=10, pady=5)

        # Move Speech Rate below Voice selection
        # Speech Rate
        speech_rate_label = ctk.CTkLabel(voice_options_grid, text="Speech Rate:")
        self.speech_rate_label = speech_rate_label
        speech_rate_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        speech_rate_slider = ctk.CTkSlider(voice_options_grid, from_=50, to=150, variable=self.speech_rate_var)
        self.speech_rate_slider = speech_rate_slider
        speech_rate_slider.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        speech_rate_value_label = ctk.CTkLabel(voice_options_grid, text="100%")
        self.speech_rate_value_label = speech_rate_value_label
        speech_rate_value_label.grid(row=1, column=2, padx=10, pady=5, sticky="w")
        # Update label when slider changes
        speech_rate_slider.configure(command=lambda val: speech_rate_value_label.configure(text=f"{int(val)}%"))

        # Refresh Voice List button - moved more to the right and below filter by location
        self.refresh_voice_button = ctk.CTkButton(voice_options_grid, text="Refresh Voice List", command=self._refresh_voice_list)
        self.refresh_voice_button.grid(row=1, column=2, columnspan=2, padx=(100, 10), pady=5, sticky="e")

        # Subtitle Settings Frame - Next in right column
        subtitle_styling_frame = ctk.CTkFrame(right_scrollable)
        subtitle_styling_frame.pack(fill=tk.X, padx=10, pady=5)

        subtitle_styling_label = ctk.CTkLabel(subtitle_styling_frame, text="Subtitle Settings", font=ctk.CTkFont(weight="bold"))
        subtitle_styling_label.pack(anchor="w", padx=10, pady=5)

        subtitle_styling_grid = ctk.CTkFrame(subtitle_styling_frame)
        subtitle_styling_grid.pack(fill=tk.X, padx=10, pady=5)

        # Enable Subtitles checkbox - Add at the top
        enable_subtitles_check = ctk.CTkCheckBox(subtitle_styling_grid, text="Enable Subtitles (ASS format)",
                                               variable=self.enable_subtitles_var,
                                               command=self._toggle_subtitle_settings)
        enable_subtitles_check.grid(row=0, column=0, columnspan=4, padx=10, pady=10, sticky="w")

        # Subtitle Font - Moved to Subtitle Settings with comprehensive font support
        subtitle_font_label = ctk.CTkLabel(subtitle_styling_grid, text="Subtitle Font:")
        self.subtitle_font_label = subtitle_font_label
        subtitle_font_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        # Get available fonts from the font directory (like main.py)
        try:
            from main import get_available_fonts
            available_fonts = get_available_fonts()
            if not available_fonts:
                # Fallback to default list if no fonts found
                available_fonts = ["TitanOne", "Ranchers", "RampartOne", "PermanentMarker", "OpenSans", "NotoSans",
                                 "Montserrat", "LuckiestGuy", "Knewave", "Jua", "Creepster", "Caveat",
                                 "Bungee", "BebasNeue", "Bangers", "BakbakOne"]
        except ImportError:
            # Fallback if import fails
            available_fonts = ["TitanOne", "Arial", "Roboto", "Open Sans", "Montserrat"]

        subtitle_font_dropdown = ctk.CTkOptionMenu(subtitle_styling_grid, variable=self.subtitle_font_var, values=available_fonts)
        subtitle_font_dropdown.grid(row=0, column=1, padx=10, pady=5)

        # Set default font (TitanOne if available, otherwise first font in list)
        if "TitanOne" in available_fonts:
            self.subtitle_font_var.set("TitanOne")
        else:
            self.subtitle_font_var.set(available_fonts[0])

        # Preview Font button
        preview_font_button = ctk.CTkButton(subtitle_styling_grid, text="Preview", width=80, command=self._preview_subtitle_font)
        preview_font_button.grid(row=0, column=2, padx=10, pady=5)

        # Font Color - Moved down after Subtitle Font
        font_color_label = ctk.CTkLabel(subtitle_styling_grid, text="Font Color:")
        font_color_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        font_color_frame = ctk.CTkFrame(subtitle_styling_grid)
        font_color_frame.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        font_color_preview = ctk.CTkButton(font_color_frame, text="", width=30, height=30, fg_color="#FFFFFF", hover_color="#FFFFFF")
        font_color_preview.pack(side=tk.LEFT, padx=(0, 10))

        font_color_button = ctk.CTkButton(font_color_frame, text="Choose Color", command=lambda: self._choose_color(self.font_color_var, font_color_preview))
        font_color_button.pack(side=tk.LEFT)

        font_color_hex = ctk.CTkLabel(font_color_frame, textvariable=self.font_color_var)
        font_color_hex.pack(side=tk.LEFT, padx=10)

        # Font Size
        font_size_label = ctk.CTkLabel(subtitle_styling_grid, text="Font Size:")
        font_size_label.grid(row=1, column=2, padx=10, pady=5, sticky="w")

        font_size_options = ["20", "30", "40", "50", "60", "70", "80"]
        font_size_dropdown = ctk.CTkOptionMenu(subtitle_styling_grid, variable=self.font_size_var,
                                              values=font_size_options,
                                              command=lambda val: self.font_size_var.set(int(val)))
        font_size_dropdown.grid(row=1, column=3, padx=10, pady=5)

        px_label = ctk.CTkLabel(subtitle_styling_grid, text="px")
        px_label.grid(row=1, column=4, padx=(0, 10), pady=5, sticky="w")

        # Outline Color
        outline_color_label = ctk.CTkLabel(subtitle_styling_grid, text="Outline Color:")
        outline_color_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")

        outline_color_frame = ctk.CTkFrame(subtitle_styling_grid)
        outline_color_frame.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        outline_color_preview = ctk.CTkButton(outline_color_frame, text="", width=30, height=30, fg_color="#000000", hover_color="#000000")
        outline_color_preview.pack(side=tk.LEFT, padx=(0, 10))

        outline_color_button = ctk.CTkButton(outline_color_frame, text="Choose Color", command=lambda: self._choose_color(self.outline_color_var, outline_color_preview))
        outline_color_button.pack(side=tk.LEFT)

        outline_color_hex = ctk.CTkLabel(outline_color_frame, textvariable=self.outline_color_var)
        outline_color_hex.pack(side=tk.LEFT, padx=10)

        # Outline Size
        outline_size_label = ctk.CTkLabel(subtitle_styling_grid, text="Outline Size:")
        outline_size_label.grid(row=2, column=2, padx=10, pady=5, sticky="w")

        outline_size_options = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
        outline_size_dropdown = ctk.CTkOptionMenu(subtitle_styling_grid, variable=self.outline_size_var,
                                                 values=outline_size_options,
                                                 command=lambda val: self.outline_size_var.set(int(val)))
        outline_size_dropdown.grid(row=2, column=3, padx=10, pady=5)

        outline_px_label = ctk.CTkLabel(subtitle_styling_grid, text="px")
        outline_px_label.grid(row=2, column=4, padx=(0, 10), pady=5, sticky="w")

        # Caption Position
        caption_position_label = ctk.CTkLabel(subtitle_styling_grid, text="Caption Position:")
        caption_position_label.grid(row=3, column=0, padx=10, pady=5, sticky="w")

        caption_position_frame = ctk.CTkFrame(subtitle_styling_grid, fg_color="transparent")
        caption_position_frame.grid(row=3, column=1, padx=10, pady=5, sticky="w")

        top_radio = ctk.CTkRadioButton(caption_position_frame, text="Top", variable=self.caption_position_var, value="Top")
        top_radio.pack(side=tk.LEFT, padx=(0, 10))

        center_radio = ctk.CTkRadioButton(caption_position_frame, text="Center", variable=self.caption_position_var, value="Center")
        center_radio.pack(side=tk.LEFT, padx=(0, 10))

        bottom_radio = ctk.CTkRadioButton(caption_position_frame, text="Bottom", variable=self.caption_position_var, value="Bottom")
        bottom_radio.pack(side=tk.LEFT)

        # Words Per Caption
        words_per_caption_label = ctk.CTkLabel(subtitle_styling_grid, text="Words Per Caption:")
        words_per_caption_label.grid(row=3, column=2, padx=10, pady=5, sticky="w")

        words_per_caption_options = ["3", "5", "7", "10", "Full"]
        words_per_caption_dropdown = ctk.CTkOptionMenu(subtitle_styling_grid, variable=self.words_per_caption_var, values=words_per_caption_options)
        words_per_caption_dropdown.grid(row=3, column=3, padx=10, pady=(5, 0))

        words_info = ctk.CTkLabel(subtitle_styling_grid, text="Number of words to show at once",
                                 font=ctk.CTkFont(size=10), text_color="silver")
        words_info.grid(row=4, column=3, padx=10, pady=(0, 5), sticky="w")

        # Highlight Style - moved to be below "Number of words to show at once" text
        self.highlight_style_label = ctk.CTkLabel(subtitle_styling_grid, text="Highlight Style:")
        self.highlight_style_label.grid(row=5, column=2, padx=10, pady=5, sticky="w")

        self.highlight_style_frame = ctk.CTkFrame(subtitle_styling_grid, fg_color="transparent")
        self.highlight_style_frame.grid(row=5, column=3, padx=10, pady=5, sticky="w")

        self.text_color_radio = ctk.CTkRadioButton(self.highlight_style_frame, text="Text Color",
                                                  variable=self.highlight_style_var, value="Text Color")
        self.text_color_radio.pack(side=tk.LEFT, padx=(0, 10))

        self.background_radio = ctk.CTkRadioButton(self.highlight_style_frame, text="Background",
                                                  variable=self.highlight_style_var, value="Background")
        self.background_radio.pack(side=tk.LEFT)

        # Word Highlighting
        word_highlighting_label = ctk.CTkLabel(subtitle_styling_grid, text="Word Highlighting:")
        word_highlighting_label.grid(row=4, column=0, padx=10, pady=5, sticky="w")

        word_highlighting_check = ctk.CTkCheckBox(subtitle_styling_grid, text="Enable word highlighting",
                                                 variable=self.word_highlighting_var,
                                                 command=self._toggle_highlighting_controls)
        word_highlighting_check.grid(row=4, column=1, padx=10, pady=5, sticky="w")

        # Highlight Color
        self.highlight_color_label = ctk.CTkLabel(subtitle_styling_grid, text="Highlight Color:")
        self.highlight_color_label.grid(row=5, column=0, padx=10, pady=5, sticky="w")

        self.highlight_color_frame = ctk.CTkFrame(subtitle_styling_grid)
        self.highlight_color_frame.grid(row=5, column=1, padx=10, pady=5, sticky="w")

        self.highlight_color_preview = ctk.CTkButton(self.highlight_color_frame, text="", width=30, height=30, fg_color="#FFFF00", hover_color="#FFFF00")
        self.highlight_color_preview.pack(side=tk.LEFT, padx=(0, 10))

        self.highlight_color_button = ctk.CTkButton(self.highlight_color_frame, text="Choose Color",
                                                   command=lambda: self._choose_color(self.highlight_color_var, self.highlight_color_preview))
        self.highlight_color_button.pack(side=tk.LEFT)

        self.highlight_color_hex = ctk.CTkLabel(self.highlight_color_frame, textvariable=self.highlight_color_var)
        self.highlight_color_hex.pack(side=tk.LEFT, padx=10)

        # Initial state of highlighting controls
        self._toggle_highlighting_controls()

        # Settings Profile section - moved below Subtitle Settings
        profile_frame = ctk.CTkFrame(right_scrollable)
        profile_frame.pack(fill=tk.X, padx=10, pady=5)

        profile_label = ctk.CTkLabel(profile_frame, text="Profile Settings", font=ctk.CTkFont(weight="bold"))
        profile_label.pack(anchor="w", padx=10, pady=5)

        profile_controls = ctk.CTkFrame(profile_frame)
        profile_controls.pack(fill=tk.X, padx=10, pady=5)

        # Profile selection
        profile_select_label = ctk.CTkLabel(profile_controls, text="Select Profile:")
        profile_select_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        self.profile_dropdown = ctk.CTkOptionMenu(profile_controls, variable=self.current_profile_var,
                                                values=["Default"], command=self._load_profile)
        self.profile_dropdown.grid(row=0, column=1, padx=10, pady=5)

        # Profile buttons
        profile_buttons = ctk.CTkFrame(profile_controls, fg_color="transparent")
        profile_buttons.grid(row=0, column=2, columnspan=3, padx=10, pady=5, sticky="e")

        save_profile_button = ctk.CTkButton(profile_buttons, text="Save", command=self._save_profile, width=80)
        save_profile_button.pack(side=tk.LEFT, padx=(0, 5))

        new_profile_button = ctk.CTkButton(profile_buttons, text="New", command=self._new_profile, width=80)
        new_profile_button.pack(side=tk.LEFT, padx=5)

        delete_profile_button = ctk.CTkButton(profile_buttons, text="Delete", command=self._delete_profile, width=80)
        delete_profile_button.pack(side=tk.LEFT, padx=5)

        # Initial check of voice settings state
        self.after(200, self._toggle_voice_settings)

        # Initialize TTS model and voices
        self.after(300, lambda: self._on_tts_model_change(self.voice_api_var.get()))

        # Bulk Generation Frame - Next in left column
        batch_generation_frame = ctk.CTkFrame(left_frame)
        batch_generation_frame.pack(fill=tk.X, padx=10, pady=5)

        batch_generation_label = ctk.CTkLabel(batch_generation_frame, text="Bulk Generation", font=ctk.CTkFont(weight="bold"))
        batch_generation_label.pack(anchor="w", padx=10, pady=5)

        batch_generation_grid = ctk.CTkFrame(batch_generation_frame)
        batch_generation_grid.pack(fill=tk.X, padx=10, pady=5)

        # Enable Bulk Generation
        enable_batch_check = ctk.CTkCheckBox(batch_generation_grid, text="Enable Bulk Generation",
                                           variable=self.enable_batch_var,
                                           command=self._toggle_batch_generation)
        enable_batch_check.grid(row=0, column=0, padx=10, pady=10, sticky="w")

        # Number of Videos
        self.batch_count_label = ctk.CTkLabel(batch_generation_grid, text="Number of Videos:")
        self.batch_count_label.grid(row=0, column=1, padx=10, pady=10, sticky="e")

        self.batch_count_entry = ctk.CTkEntry(batch_generation_grid, textvariable=self.batch_count_var, width=60)
        self.batch_count_entry.grid(row=0, column=2, padx=10, pady=10, sticky="w")

        # Upload Bulk Scripts button - Rename and make disableable
        self.upload_bulk_scripts_button = ctk.CTkButton(batch_generation_grid, text="Upload Bulk Scripts", command=self._upload_bulk_scripts)
        self.upload_bulk_scripts_button.grid(row=1, column=0, columnspan=3, padx=10, pady=(0, 10), sticky="w")

        # Bulk scripts file path
        self.bulk_scripts_var = tk.StringVar()
        bulk_scripts_path = ctk.CTkLabel(batch_generation_grid, textvariable=self.bulk_scripts_var,
                                       font=ctk.CTkFont(size=10), text_color="#888888")
        bulk_scripts_path.grid(row=2, column=0, columnspan=3, padx=10, pady=(0, 10), sticky="w")

        # Initial state of batch generation components
        self._toggle_batch_generation()
        self._toggle_custom_titles_file()

        # Buttons frame - Bottom of the main window
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        # Control buttons (enhanced)
        control_buttons_frame = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        control_buttons_frame.pack(side=tk.RIGHT)

        self.pause_button = ctk.CTkButton(
            control_buttons_frame,
            text="PAUSE",
            command=self._pause_generation,
            width=100
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 10), pady=(10, 0))
        self.pause_button.configure(state="disabled")

        self.stop_button = ctk.CTkButton(
            control_buttons_frame,
            text="STOP",
            command=self._stop_generation,
            fg_color="#D32F2F",  # Red color
            hover_color="#B71C1C",
            width=100
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10), pady=(10, 0))
        self.stop_button.configure(state="disabled")

        self.generate_button = ctk.CTkButton(
            control_buttons_frame,
            text="GENERATE VIDEO",
            command=self.generate_video,
            height=40,  # Increased height
            font=ctk.CTkFont(size=14, weight="bold"),  # Larger font
            width=200,
            fg_color="#F37200",  # Updated orange color
            hover_color="#E66600",  # Darker hover color
            text_color="white"  # White text
        )
        self.generate_button.pack(side=tk.LEFT, padx=0, pady=(10, 0))

        # Refresh Resources button (like main.py)
        refresh_button = ctk.CTkButton(
            control_buttons_frame,
            text="🔄 Refresh",
            command=self._refresh_resources,
            height=40,
            width=100,
            font=ctk.CTkFont(size=12),
            fg_color="#4A90E2",
            hover_color="#357ABD",
            text_color="white"
        )
        refresh_button.pack(side=tk.LEFT, padx=(10, 0), pady=(10, 0))

        # Progress display - Moved below control buttons
        progress_display_frame = ctk.CTkFrame(buttons_frame, fg_color="transparent")
        progress_display_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        # Top row with progress info and Open Folder button (inline)
        progress_top_frame = ctk.CTkFrame(progress_display_frame, fg_color="transparent")
        progress_top_frame.pack(fill=tk.X, padx=10, pady=(0, 5))

        self.progress_info_var = tk.StringVar(value="Generation Status")
        progress_info_label = ctk.CTkLabel(progress_top_frame, textvariable=self.progress_info_var, font=ctk.CTkFont(weight="bold"))
        progress_info_label.pack(side=tk.LEFT, anchor="w")

        # Open Folder button (initially hidden, will be shown inline after video completion)
        self.open_folder_button = ctk.CTkButton(
            progress_top_frame,
            text="📁 Open Folder",
            command=self._open_video_folder,
            width=90,
            height=20,
            font=ctk.CTkFont(size=11)
        )
        # Don't pack initially - will be packed when video completes

        # Bottom row with status and progress bar
        progress_bottom_frame = ctk.CTkFrame(progress_display_frame, fg_color="transparent")
        progress_bottom_frame.pack(fill=tk.X, padx=10)

        self.status_var = tk.StringVar(value="Generation Status")
        status_label = ctk.CTkLabel(progress_bottom_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=10)

        self.progress_bar = ctk.CTkProgressBar(progress_bottom_frame)
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=10)
        self.progress_bar.set(0)

        # Initial update of input frame
        self._update_input_frame()

    def _update_api_colors(self, *args):
        """Update API dropdown colors based on key validity (placeholder)"""
        # For now, just use default colors
        # API key validation will be implemented later
        pass

    # Duration field toggle removed since duration field was removed

    def _preview_voice(self):
        """Preview the selected voice"""
        selected_voice = self.voice_var.get()
        selected_model = self.tts_model_var.get()
        selected_rate = int(self.speech_rate_var.get())

        # Display preview status
        self.status_var.set(f"Previewing voice: {selected_voice} with {selected_model} at {selected_rate}% rate")

        # Placeholder for actual voice preview
        # In a real implementation, this would call the appropriate voice API
        print(f"Previewing voice: {selected_voice} with {selected_model} at {selected_rate}% rate")

        # Reset status after a brief delay
        self.after(3000, lambda: self.status_var.set("Generation Status"))

    def _preview_voice_enhanced(self):
        """Enhanced voice preview using the voice_preview module from main.py"""
        if not IMPORTS_SUCCESSFUL:
            self.status_var.set("Voice preview not available - import error")
            self.after(2000, lambda: self.status_var.set("Generation Status"))
            return

        try:
            import voice_preview
            selected_voice = self.voice_var.get()
            selected_model = self.voice_api_var.get()  # Use the TTS model from voice API

            # Map the TTS model names
            if selected_model == "Voicely":
                tts_model = "Voicely"
            elif selected_model == "ElevenLabs":
                tts_model = "ElevenLabs"
            else:
                tts_model = "OpenAI"

            # Display preview status
            self.status_var.set(f"Generating voice preview for {selected_voice}...")

            # Disable the preview button temporarily
            self.preview_voice_button.configure(state="disabled")

            def on_preview_complete():
                """Callback when preview completes"""
                self.preview_voice_button.configure(state="normal")
                self.status_var.set("Voice preview completed")
                self.after(2000, lambda: self.status_var.set("Generation Status"))

            # Generate and play the preview
            voice_preview.generate_and_play_preview(selected_voice, tts_model, on_preview_complete)

        except Exception as e:
            self.status_var.set(f"Error previewing voice: {str(e)}")
            self.preview_voice_button.configure(state="normal")
            self.after(3000, lambda: self.status_var.set("Generation Status"))

    def _refresh_voice_list(self):
        """Refresh the list of available voices with proper locale filtering"""
        selected_model = self.tts_model_var.get()
        selected_locale = self.filter_locale_var.get()

        # Display status
        self.status_var.set(f"Refreshing voice list for {selected_model} (locale: {selected_locale})...")

        try:
            if selected_model == "Voicely":
                # Load Voicely/Edge TTS voices from JSON with locale filtering
                try:
                    import json
                    script_dir = os.path.dirname(os.path.abspath(__file__))
                    json_path = os.path.join(script_dir, "src", "english_edge_voices.json")

                    with open(json_path, "r") as f:
                        english_voices = json.load(f)

                    # Filter voices by locale if needed
                    if selected_locale != "All":
                        filtered_voices = [v for v in english_voices if v["Locale"] == selected_locale]
                    else:
                        filtered_voices = english_voices

                    # Create voice options list
                    voices = [voice["ShortName"] for voice in filtered_voices]
                    voices.sort()

                    if not voices:
                        voices = ["No voices found for this locale"]

                except Exception as e:
                    print(f"Error loading Voicely voices: {e}")
                    # Fallback to default list
                    if selected_locale == "All" or selected_locale == "en-US":
                        voices = ["en-US-AriaNeural", "en-US-JennyNeural", "en-US-GuyNeural", "en-US-AndrewNeural"]
                    elif selected_locale == "en-GB":
                        voices = ["en-GB-SoniaNeural", "en-GB-RyanNeural"]
                    elif selected_locale == "en-AU":
                        voices = ["en-AU-NatashaNeural", "en-AU-WilliamNeural"]
                    elif selected_locale == "en-CA":
                        voices = ["en-CA-ClaraNeural", "en-CA-LiamNeural"]
                    else:
                        voices = ["en-US-AriaNeural", "en-US-JennyNeural"]

            elif selected_model == "ElevenLabs":
                # ElevenLabs voices (no locale filtering needed)
                try:
                    if IMPORTS_SUCCESSFUL:
                        from elevenlabs_client import elevenlabs_client
                        if elevenlabs_client.api_key:
                            elevenlabs_voices = elevenlabs_client.get_voices()
                            if elevenlabs_voices:
                                voices = [voice['name'] for voice in elevenlabs_voices]
                            else:
                                voices = ["No ElevenLabs voices found"]
                        else:
                            voices = ["ElevenLabs API key not set"]
                    else:
                        voices = ["Rachel", "Domi", "Bella", "Antoni", "Josh", "Arnold", "Adam", "Sam"]
                except Exception as e:
                    print(f"Error loading ElevenLabs voices: {e}")
                    voices = ["Rachel", "Domi", "Bella", "Antoni"]

            elif selected_model == "OpenAI":
                # OpenAI voices (no locale filtering needed)
                voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]

            else:
                voices = ["Default Voice"]

            # Update voice dropdown
            self.voice_dropdown.configure(values=voices)
            if voices and voices[0] != "No voices found for this locale":
                self.voice_var.set(voices[0])
            else:
                self.voice_var.set("No voices available")

            # Update status
            self.status_var.set(f"Voice list refreshed successfully! Found {len(voices)} voices.")
            self.after(2000, lambda: self.status_var.set("Generation Status"))

        except Exception as e:
            print(f"Error in _refresh_voice_list: {e}")
            self.status_var.set(f"Error refreshing voices: {str(e)}")
            self.after(3000, lambda: self.status_var.set("Generation Status"))

    def _on_locale_change(self, selected_locale):
        """Handle locale filter change - automatically refresh voice list"""
        # Only refresh if Voicely is selected (other TTS models don't use locale filtering)
        if self.voice_api_var.get() == "Voicely":
            self._refresh_voice_list()

    def _choose_color(self, color_var, preview_button):
        """Open a color picker and update the color variable and preview"""
        try:
            # Try to use the tkinter colorchooser
            from tkinter import colorchooser
            initial_color = color_var.get()
            # Remove # if present for colorchooser
            if initial_color.startswith('#'):
                initial_color = initial_color[1:]
            # Convert hex to RGB tuple
            r = int(initial_color[0:2], 16) if len(initial_color) >= 2 else 0
            g = int(initial_color[2:4], 16) if len(initial_color) >= 4 else 0
            b = int(initial_color[4:6], 16) if len(initial_color) >= 6 else 0

            # Open color picker
            color_result = colorchooser.askcolor(color=f"#{initial_color}", title="Choose Color")

            if color_result[1]:  # If color was selected (not cancelled)
                hex_color = color_result[1].upper()
                color_var.set(hex_color)
                preview_button.configure(fg_color=hex_color, hover_color=hex_color)

                # Update status
                self.status_var.set(f"Color updated to: {hex_color}")
                self.after(2000, lambda: self.status_var.set("Generation Status"))
        except Exception as e:
            print(f"Error in color picker: {e}")
            self.status_var.set(f"Error in color picker: {e}")
            self.after(2000, lambda: self.status_var.set("Generation Status"))

    def _toggle_custom_title(self):
        """Toggle custom title input based on checkbox state - DEPRECATED"""
        pass

    def _toggle_custom_script(self):
        """Toggle custom script input based on checkbox state - DEPRECATED"""
        pass

    def _toggle_batch_generation(self):
        """Toggle batch generation settings based on checkbox state"""
        if self.enable_batch_var.get():
            self.batch_count_label.configure(state="normal")
            self.batch_count_entry.configure(state="normal")
            self.upload_bulk_scripts_button.configure(state="normal")

            # Also disable duration field since bulk generation is enabled
            self._toggle_duration_field()
        else:
            self.batch_count_label.configure(state="disabled")
            self.batch_count_entry.configure(state="disabled")
            self.upload_bulk_scripts_button.configure(state="disabled")

            # Re-enable duration field if needed
            self._toggle_duration_field()

    def _toggle_custom_titles_file(self):
        """Toggle custom titles file settings based on checkbox state - DEPRECATED"""
        pass

    def _get_widget_fg_color(self):
        """Get the appropriate foreground color based on the current theme"""
        if ctk.get_appearance_mode() == "Dark":
            return "#2B2B2B"  # Dark theme frame background
        else:
            return "#EBEBEB"  # Light theme frame background

    def _get_disabled_color(self):
        """Get the appropriate disabled color based on the current theme"""
        if ctk.get_appearance_mode() == "Dark":
            return "#1E1E1E"  # Darker shade for dark theme
        else:
            return "#D9D9D9"  # Lighter gray for light theme

    def _get_available_music_files(self):
        """Get available music files from the music directory (like main.py)"""
        music_options = ["None"]

        try:
            # Get the music directory path
            script_dir = os.path.dirname(os.path.abspath(__file__))
            music_dir = os.path.join(os.path.dirname(script_dir), "music")

            # Create music directory if it doesn't exist
            os.makedirs(music_dir, exist_ok=True)

            # Scan for MP3 files in the music directory
            if os.path.exists(music_dir):
                for file in os.listdir(music_dir):
                    if file.lower().endswith(".mp3"):
                        # Use the filename without extension as the display name
                        music_name = os.path.splitext(file)[0]
                        music_options.append(music_name)

            # Don't add mock data - only show real music files
            # If no music files found, only show "None"

        except Exception as e:
            print(f"Error scanning music directory: {e}")
            # Only show "None" if there's an error - no mock data
            music_options = ["None"]

        return music_options

    def _refresh_resources(self):
        """Refresh fonts and music resources (like main.py)"""
        try:
            self.status_var.set("Refreshing resources (fonts and music)...")

            # Refresh fonts
            try:
                from main import get_available_fonts
                available_fonts = get_available_fonts()
                if not available_fonts:
                    available_fonts = ["TitanOne", "Ranchers", "RampartOne", "PermanentMarker", "OpenSans", "NotoSans",
                                     "Montserrat", "LuckiestGuy", "Knewave", "Jua", "Creepster", "Caveat",
                                     "Bungee", "BebasNeue", "Bangers", "BakbakOne"]

                # Update font dropdown if it exists
                if hasattr(self, 'subtitle_font_dropdown'):
                    current_font = self.subtitle_font_var.get()
                    self.subtitle_font_dropdown.configure(values=available_fonts)
                    if current_font in available_fonts:
                        self.subtitle_font_var.set(current_font)
                    else:
                        self.subtitle_font_var.set(available_fonts[0] if available_fonts else "TitanOne")

            except ImportError:
                pass

            # Refresh music files
            music_options = self._get_available_music_files()
            current_music = self.background_music_var.get()
            self.bg_music_dropdown.configure(values=music_options)

            # Try to keep the previously selected music if it still exists
            if current_music in music_options:
                self.background_music_var.set(current_music)
            else:
                self.background_music_var.set("None")

            self.status_var.set("Resources refreshed successfully!")
            self.after(2000, lambda: self.status_var.set("Generation Status"))

        except Exception as e:
            self.status_var.set(f"Error refreshing resources: {str(e)}")
            self.after(3000, lambda: self.status_var.set("Generation Status"))

    def _browse_titles_file(self):
        """Open file dialog to select a titles file"""
        filename = filedialog.askopenfilename(
            title="Select Titles File",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )
        if filename:
            self.custom_titles_file_var.set(filename)

    def _load_sample_script(self):
        """Load a sample script - DEPRECATED"""
        # Script Options section has been removed
        pass

    def _load_sample_titles(self):
        """Create a sample titles file and load it"""
        try:
            # Create sample titles
            sample_titles = [
                "The Hidden World of Deep Sea Creatures",
                "Exploring Ancient Mayan Ruins",
                "The Science Behind Northern Lights",
                "How Mountains Are Formed",
                "The Secret Life of Bees"
            ]

            # Create a temporary file to store these titles
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.txt', delete=False) as temp:
                temp.write('\n'.join(sample_titles))
                temp_filename = temp.name

            # Set the file path
            self.custom_titles_file_var.set(temp_filename)

            # Update batch count to match the number of titles
            self.batch_count_var.set(str(len(sample_titles)))

            # Update status
            self.status_var.set(f"Sample titles loaded from {temp_filename}")
            self.after(4000, lambda: self.status_var.set("Generation Status"))

        except Exception as e:


            self.status_var.set(f"Error loading sample titles: {e}")
            self.after(2000, lambda: self.status_var.set("Generation Status"))

    def _pause_generation(self):
        """Pause the current generation process"""
        if self.is_generating_var.get() and self.current_generation_process:
            self.status_var.set("Pausing generation... (will complete current step)")
            # Implement actual pause logic here
            self._log_to_console("Generation paused by user")

    def _stop_generation(self):
        """Stop the current generation process"""
        if self.is_generating_var.get() and self.current_generation_process:
            self.status_var.set("Stopping generation...")

            try:
                # Terminate the subprocess
                if self.current_generation_process:
                    self.current_generation_process.terminate()
                    self.current_generation_process = None

                # Reset UI state
                self.is_generating_var.set(False)
                self.progress_bar.set(0)
                self.progress_info_var.set("Generation stopped by user")
                self._update_generation_controls(False)

                self._log_to_console("Generation stopped by user")

                # Show message to user
                messagebox.showinfo("Generation Stopped", "Video generation was stopped by the user.")
            except Exception as e:

                self.status_var.set(f"Error stopping generation: {e}")
                self._log_to_console(f"Error stopping generation: {e}")

    def _log_to_console(self, message):
        """Add a message to the console log"""
        # Since we removed the console log, just update the status
        from datetime import datetime
        timestamp = datetime.now().strftime("[%H:%M:%S]")
        # Update the status with the latest message
        self.status_var.set(message)
        # Also print to console for debugging
        print(f"{timestamp} {message}")

    def _update_generation_controls(self, is_generating):
        """Update UI controls based on generation state"""
        if is_generating:
            # Disable input controls
            self.generate_button.configure(state="disabled")
            self.pause_button.configure(state="normal")
            self.stop_button.configure(state="normal")
        else:
            # Enable input controls
            self.generate_button.configure(state="normal")
            self.pause_button.configure(state="disabled")
            self.stop_button.configure(state="disabled")

    def _update_input_frame(self):
        """Update the input content frame based on the selected input type"""
        # Clear existing widgets
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # Duration dropdown reset removed since duration field was removed

        input_type = self.input_type_var.get()

        if input_type == "topic":
            # Topic input
            topic_label = ctk.CTkLabel(self.content_frame, text="Enter a video topic:")
            topic_label.pack(anchor="w", padx=10, pady=5)

            topic_entry = ctk.CTkEntry(self.content_frame, textvariable=self.topic_var, width=600)
            topic_entry.pack(fill=tk.X, padx=10, pady=5)

            examples_label = ctk.CTkLabel(self.content_frame, text="Examples: 'The Future of AI', 'Climate Change Solutions', 'How to Train Your Dog'")
            examples_label.pack(anchor="w", padx=10, pady=5)

        elif input_type == "custom":
            # Custom script input
            script_label = ctk.CTkLabel(self.content_frame, text="Enter your custom script:")
            script_label.pack(anchor="w", padx=10, pady=5)

            # Create a frame for the text area with scrollbar
            script_frame = ctk.CTkFrame(self.content_frame)
            script_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            script_text = ctk.CTkTextbox(script_frame, height=130)  # Reduced height to fit in fixed content frame
            script_text.pack(fill=tk.BOTH, expand=True)

            # Set existing content if any
            existing_content = self.custom_script_var.get()
            if existing_content:
                script_text.insert("1.0", existing_content)

            # Bind multiple events to capture text changes
            def update_script_var(event=None):
                content = script_text.get("1.0", tk.END).strip()
                self.custom_script_var.set(content)

            script_text.bind("<KeyRelease>", update_script_var)
            script_text.bind("<ButtonRelease>", update_script_var)
            script_text.bind("<FocusOut>", update_script_var)

            format_label = ctk.CTkLabel(self.content_frame, text="Format: Use 'Scene 1:', 'Scene 2:', etc. to mark different scenes.")
            format_label.pack(anchor="w", padx=10, pady=5)

        elif input_type == "file":
            # Script file input
            file_label = ctk.CTkLabel(self.content_frame, text="Select a script file:")
            file_label.pack(anchor="w", padx=10, pady=5)

            file_frame = ctk.CTkFrame(self.content_frame)
            file_frame.pack(fill=tk.X, padx=10, pady=5)

            file_entry = ctk.CTkEntry(file_frame, textvariable=self.script_file_var, width=400)
            file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

            # Replace the two buttons with a single Upload button
            upload_button = ctk.CTkButton(file_frame, text="Upload", command=self.browse_script_file, width=80)
            upload_button.pack(side=tk.RIGHT, padx=10)

            # Show selected file path
            if self.script_file_var.get():
                file_path_label = ctk.CTkLabel(self.content_frame,
                                             text=f"Selected: {os.path.basename(self.script_file_var.get())}",
                                             font=ctk.CTkFont(size=10),
                                             text_color="#888888")
                file_path_label.pack(anchor="w", padx=10, pady=(0, 5))

        # Duration field toggle removed since duration field was removed

    def browse_script_file(self):
        """Open file dialog to select a script file"""
        filename = filedialog.askopenfilename(
            title="Select Script File",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )
        if filename:
            self.script_file_var.set(filename)

    def generate_video(self):
        """Generate video using the VideoGeneratorThread approach like main.py"""
        if not IMPORTS_SUCCESSFUL:
            messagebox.showerror("Import Error", "Required modules are not available. Please check your installation.")
            return

        # Check if already generating
        if hasattr(self, 'current_generation_thread') and self.current_generation_thread and self.current_generation_thread.is_alive():
            messagebox.showinfo("Generation in Progress", "Video generation is already in progress.")
            return

        # Validate inputs first
        if not self._validate_inputs():
            return

        try:
            # Reset status
            self.status_var.set("Initializing video generation...")
            self.progress_bar.set(0)
            self.progress_info_var.set("Starting video generation...")

            # Set generating state
            self.is_generating_var.set(True)
            self._update_generation_controls(True)

            # Create the video generator thread
            generator_thread = self._create_video_generator_thread()

            if generator_thread is None:
                messagebox.showerror("Error", "Failed to create video generator. Please check your settings.")
                self.is_generating_var.set(False)
                self._update_generation_controls(False)
                return

            # Store the thread reference
            self.current_generation_thread = generator_thread

            # Start the thread
            generator_thread.start()

            # Start monitoring the thread
            self._monitor_generation_thread()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start video generation: {str(e)}")
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            self.status_var.set(f"Error: {str(e)}")
            print(f"Error in generate_video: {e}")
            import traceback
            traceback.print_exc()

    def _monitor_generation_thread(self):
        """Monitor the video generation thread and update UI accordingly"""
        if self.current_generation_thread and self.current_generation_thread.is_alive():
            # Thread is still running, check again in 100ms
            self.after(100, self._monitor_generation_thread)
        else:
            # Thread has finished
            if self.current_generation_thread:
                result = self.current_generation_thread.result
                if result and result.get("success"):
                    video_path = result.get("video_path")
                    if video_path:
                        self.status_var.set("Video generation completed successfully!")
                        self.progress_bar.set(1.0)
                        self.progress_info_var.set(f"Video saved: {os.path.basename(video_path)}")
                        # Show the Open Folder button
                        self.open_folder_button.pack(side=tk.LEFT, padx=(10, 0))
                        self.last_generated_video_path = video_path
                        messagebox.showinfo("Success", "Video generation completed successfully!")
                    else:
                        self.status_var.set("Video generation completed but no path returned")
                        self.progress_bar.set(1.0)
                else:
                    error_message = result.get("message", "Unknown error") if result else "Unknown error"
                    self.status_var.set(f"Video generation failed: {error_message}")
                    self.progress_bar.set(0)
                    messagebox.showerror("Generation Failed", f"Video generation failed: {error_message}")

            # Reset generation state
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            self.current_generation_thread = None

    def _update_status_from_thread(self, message):
        """Update status from the video generation thread"""
        self.status_var.set(message)

    def _update_progress_from_thread(self, progress):
        """Update progress from the video generation thread"""
        self.progress_bar.set(progress / 100.0)

    def _open_video_folder(self):
        """Open the folder containing the last generated video"""
        if hasattr(self, 'last_generated_video_path') and self.last_generated_video_path:
            import subprocess
            import platform

            folder_path = os.path.dirname(self.last_generated_video_path)

            try:
                if platform.system() == "Windows":
                    subprocess.run(["explorer", folder_path])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", folder_path])
                else:  # Linux
                    subprocess.run(["xdg-open", folder_path])
            except Exception as e:
                messagebox.showerror("Error", f"Could not open folder: {str(e)}")
        else:
            messagebox.showinfo("No Video", "No video has been generated yet.")

    def _run_generation(self, cmd_args):
        """Run the video generation process"""
        try:
            # Update status
            self.status_var.set("Initializing...")
            self.progress_info_var.set("Starting Video Generation")
            self.progress_bar.set(0.1)

            # Custom script functionality has been removed

            # Prepare full command with script path
            full_cmd = ["python", os.path.join(os.path.dirname(__file__), "..", "generate_video.py")]
            full_cmd.extend(cmd_args)

            # Print command for debugging
            cmd_str = ' '.join(full_cmd)
            print(f"Running command: {cmd_str}")
            self._log_to_console(f"Running generation command")

            # Create process
            self.current_generation_process = subprocess.Popen(
                full_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Track progress
            current_step = "initializing"
            for line in self.current_generation_process.stdout:
                line_text = line.strip()
                print(line_text)  # Echo to console

                # Add to log
                self._log_to_console(line_text)

                # Update progress based on output
                if "Generating script" in line:
                    current_step = "script"
                    self.status_var.set("Generating script...")
                    self.progress_bar.set(0.2)
                elif "Generating image prompts" in line:
                    current_step = "prompts"
                    self.status_var.set("Generating image prompts...")
                    self.progress_bar.set(0.3)
                elif "Generating images" in line:
                    current_step = "images"
                    self.status_var.set("Generating images...")
                    self.progress_bar.set(0.4)
                elif "Image generation completed" in line:
                    current_step = "images_done"
                    self.status_var.set("Images completed")
                    self.progress_bar.set(0.6)
                elif "Generating voice" in line:
                    current_step = "voice"
                    self.status_var.set("Generating voice narration...")
                    self.progress_bar.set(0.7)
                elif "Voice generation disabled" in line:
                    current_step = "voice_disabled"
                    self.status_var.set("Voice generation disabled - skipping to video rendering")
                    self.progress_bar.set(0.8)
                elif "Voice narration generated" in line:
                    current_step = "voice_done"
                    self.status_var.set("Voice narration completed")
                    self.progress_bar.set(0.8)
                elif "Rendering video" in line:
                    current_step = "video"
                    self.status_var.set("Rendering final video...")
                    self.progress_bar.set(0.9)
                elif "Video rendered successfully" in line:
                    current_step = "done"
                    self.status_var.set("Video completed!")
                    self.progress_bar.set(1.0)
                    # Extract output file path
                    if ":" in line:
                        output_file = line.split(":", 1)[1].strip()
                        self.last_generated_video_path = output_file  # Store the full path
                        self.progress_info_var.set(f"Video saved to: {os.path.basename(output_file)}")
                        # Show the Open Folder button inline with the message
                        self.open_folder_button.pack(side=tk.LEFT, padx=(10, 0))
                elif "Error" in line or "ERROR" in line or "Failed" in line:
                    self.status_var.set(f"Error: {line_text}")

            # Get return code
            return_code = self.current_generation_process.wait()
            self.current_generation_process = None

            # Reset generation state
            self.is_generating_var.set(False)
            self._update_generation_controls(False)

            # Final status update
            if return_code == 0:
                if current_step != "done":
                    self.status_var.set("Process completed successfully")
                    self.progress_bar.set(1.0)

                # Log success
                self._log_to_console("Video generation completed successfully!")

                # Show success message
                messagebox.showinfo("Success", "Video generation completed successfully!")
            else:
                self.status_var.set(f"Process failed with code {return_code}")
                self.progress_bar.set(0)
                messagebox.showerror("Error", f"Video generation failed with code {return_code}")

        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            self.progress_bar.set(0)
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            import traceback
            traceback.print_exc()

    def _run_integrated_generation(self):
        """Run video generation using the integrated VideoGeneratorThread"""
        if not IMPORTS_SUCCESSFUL:
            messagebox.showerror("Import Error", "Video generation functionality is not available due to import errors.\n\nPlease check your Python environment and dependencies.")
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            return

        try:
            # Map GUI parameters to VideoGeneratorThread parameters

            # Determine input type and content
            input_type = self.input_type_var.get()
            custom_script = None
            custom_title = None
            ai_custom_title = None

            if input_type == "topic":
                # For topic-based generation, we'll let the AI generate the story
                # The topic will be used as a custom title for the AI generation
                ai_custom_title = self.topic_var.get()
            elif input_type == "custom":
                # For custom script, use the script content directly
                custom_script = self.custom_script_var.get()
            elif input_type == "file":
                # For script file, read the file content
                script_file = self.script_file_var.get()
                try:
                    with open(script_file, 'r', encoding='utf-8') as f:
                        custom_script = f.read()
                except Exception as e:
                    messagebox.showerror("File Error", f"Could not read script file: {e}")
                    self.is_generating_var.set(False)
                    self._update_generation_controls(False)
                    return

            # Map story type
            story_type_mapping = {
                "Fun Facts": "fun_facts",
                "Life Pro Tips": "life_pro_tips",
                "Philosophy": "philosophy",
                "Motivational": "motivational",
                "Educational": "educational"
            }
            story_type = story_type_mapping.get(self.story_type_var.get(), "fun_facts")

            # Map image style - Use comprehensive image style variable
            image_style = self.image_style_var.get()

            # Map TTS model based on voice API
            voice_api = self.voice_api_var.get()
            if voice_api == "ElevenLabs":
                tts_model = "ElevenLabs"
            elif voice_api == "Voicely":
                tts_model = "Voicely"
            else:
                tts_model = "OpenAI"  # Default

            # Map image model based on image API
            image_api = self.image_api_var.get()
            if image_api == "Together AI Flux":
                image_model = "Together AI Flux"
            elif image_api == "Replicate Flux":
                image_model = "Replicate Flux"
            elif image_api == "FAL AI Flux":
                image_model = "FAL AI Flux"
            else:
                image_model = "Together AI Flux"  # Default

            # Get voice name
            voice_name = self.voice_var.get()

            # Get font settings
            font_name = self.subtitle_font_var.get()
            font_color = self.font_color_var.get()
            font_size = self.font_size_var.get()
            outline_color = self.outline_color_var.get()
            outline_size = self.outline_size_var.get()

            # Get caption settings
            caption_position = self.caption_position_var.get().lower()
            words_per_caption = self.words_per_caption_var.get()
            if words_per_caption == "Full":
                caption_words = 999  # Large number for full captions
            else:
                caption_words = int(words_per_caption)

            # Get highlighting settings
            highlight_words = self.word_highlighting_var.get()
            highlight_color = self.highlight_color_var.get()
            highlight_style = self.highlight_style_var.get().lower().replace(" ", "_")

            # Get background music settings - Enhanced implementation like main.py
            bg_music = self.background_music_var.get()
            bg_music_path = None
            if bg_music != "None":
                # Get the music file path directly from the music directory
                script_dir = os.path.dirname(os.path.abspath(__file__))
                music_dir = os.path.join(os.path.dirname(script_dir), "music")
                bg_music_path = os.path.join(music_dir, f"{bg_music}.mp3")

                # Verify the file exists
                if not os.path.exists(bg_music_path):
                    print(f"Warning: Music file not found: {bg_music_path}")
                    bg_music_path = None

            bg_music_volume = self.music_volume_var.get() / 100.0  # Convert percentage to decimal

            # Get LLM provider
            llm_provider = self.script_api_var.get().title()  # Convert to title case

            # Get other settings
            end_pause_duration = self.end_pause_duration_var.get()

            # Map aspect ratio to orientation
            aspect_ratio = self.aspect_var.get()
            if aspect_ratio == "9:16":
                orientation = "portrait"
            elif aspect_ratio == "16:9":
                orientation = "landscape"
            else:
                orientation = "square"  # For 1:1

            video_quality = self.video_quality_var.get()

            # Create and start the VideoGeneratorThread
            self.current_generation_thread = VideoGeneratorThread(
                story_type=story_type,
                image_style=image_style,
                tts_model=tts_model,
                image_model=image_model,
                voice_name=voice_name,
                font_name=font_name,
                font_color=font_color,
                font_size=font_size,
                outline_color=outline_color,
                outline_size=outline_size,
                caption_position=caption_position,
                caption_words=caption_words,
                callback=self._update_status_from_thread,
                update_progress=self._update_progress_from_thread,
                highlight_words=highlight_words,
                highlight_color=highlight_color,
                custom_script=custom_script,
                custom_title=custom_title,
                bg_music_path=bg_music_path,
                bg_music_volume=bg_music_volume,
                llm_provider=llm_provider,
                ai_custom_title=ai_custom_title,
                end_pause_duration=end_pause_duration,
                orientation=orientation,
                video_quality=video_quality,
                highlight_style=highlight_style
            )

            # Start the thread
            self.current_generation_thread.start()

            # Start monitoring the thread
            self._monitor_generation_thread()

        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            self.progress_bar.set(0)
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            import traceback
            traceback.print_exc()

    def _update_status_from_thread(self, message):
        """Update status from the VideoGeneratorThread (thread-safe)"""
        # Schedule the update on the main thread
        self.after(0, lambda: self.status_var.set(message))
        self.after(0, lambda: self._log_to_console(message))

    def _update_progress_from_thread(self, progress):
        """Update progress from the VideoGeneratorThread (thread-safe)"""
        # Schedule the update on the main thread
        self.after(0, lambda: self.progress_bar.set(progress / 100.0))

    def _monitor_generation_thread(self):
        """Monitor the generation thread and update UI when complete"""
        if self.current_generation_thread and self.current_generation_thread.is_alive():
            # Thread is still running, check again in 100ms
            self.after(100, self._monitor_generation_thread)
        else:
            # Thread has completed
            if self.current_generation_thread and hasattr(self.current_generation_thread, 'result'):
                result = self.current_generation_thread.result
                if result:
                    if result.get("success"):
                        # Success
                        video_path = result.get("video_path")
                        if video_path:
                            self.last_generated_video_path = video_path
                            self.progress_info_var.set(f"Video saved to: {os.path.basename(video_path)}")
                            # Show the Open Folder button
                            self.open_folder_button.pack(side=tk.LEFT, padx=(10, 0))

                        self.status_var.set("Video generation completed!")
                        self.progress_bar.set(1.0)
                        messagebox.showinfo("Success", "Video generation completed successfully!")
                    else:
                        # Error
                        error_message = result.get("message", "Unknown error")
                        self.status_var.set(f"Error: {error_message}")
                        self.progress_bar.set(0)
                        messagebox.showerror("Error", f"Video generation failed: {error_message}")

            # Reset generation state
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            self.current_generation_thread = None

    def _run_batch_generation(self):
        """Run batch video generation"""
        if not IMPORTS_SUCCESSFUL:
            messagebox.showerror("Import Error", "Video generation functionality is not available due to import errors.\n\nPlease check your Python environment and dependencies.")
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            return

        try:
            # Get batch count
            batch_count = self.batch_count_var.get()

            # Start batch generation in a separate thread
            threading.Thread(target=self._run_batch_generation_thread, args=(batch_count,), daemon=True).start()

        except Exception as e:
            self.status_var.set(f"Error: {str(e)}")
            self.progress_bar.set(0)
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
            self.is_generating_var.set(False)
            self._update_generation_controls(False)
            import traceback
            traceback.print_exc()

    def _run_batch_generation_thread(self, batch_count):
        """Run batch generation in a separate thread"""
        try:
            for i in range(batch_count):
                if not self.is_generating_var.get():
                    # Generation was stopped
                    break

                # Update status
                self.after(0, lambda i=i: self.status_var.set(f"Generating video {i+1} of {batch_count}..."))

                # Create a new VideoGeneratorThread for each video
                self.current_generation_thread = self._create_video_generator_thread()

                # Start the thread and wait for completion
                self.current_generation_thread.start()

                # Wait for completion
                while self.current_generation_thread.is_alive():
                    if not self.is_generating_var.get():
                        # Generation was stopped
                        self.current_generation_thread.stop()
                        break
                    time.sleep(0.1)

                # Check result
                if hasattr(self.current_generation_thread, 'result'):
                    result = self.current_generation_thread.result
                    if result and result.get("success"):
                        video_path = result.get("video_path")
                        if video_path:
                            self.after(0, lambda path=video_path: self._log_to_console(f"Video {i+1} completed: {os.path.basename(path)}"))
                    else:
                        error_message = result.get("message", "Unknown error") if result else "Unknown error"
                        self.after(0, lambda msg=error_message: self._log_to_console(f"Video {i+1} failed: {msg}"))

                # Update progress
                progress = ((i + 1) / batch_count) * 100
                self.after(0, lambda p=progress: self.progress_bar.set(p / 100.0))

            # Batch generation completed
            self.after(0, lambda: self.status_var.set(f"Batch generation completed! Generated {batch_count} videos."))
            self.after(0, lambda: messagebox.showinfo("Batch Complete", f"Successfully generated {batch_count} videos!"))

        except Exception as e:
            self.after(0, lambda: self.status_var.set(f"Batch generation error: {str(e)}"))
            self.after(0, lambda: messagebox.showerror("Batch Error", f"Batch generation failed: {str(e)}"))
        finally:
            # Reset generation state
            self.after(0, lambda: self.is_generating_var.set(False))
            self.after(0, lambda: self._update_generation_controls(False))
            self.current_generation_thread = None

    def _create_video_generator_thread(self):
        """Create a VideoGeneratorThread with current GUI settings"""
        # Map GUI parameters to VideoGeneratorThread parameters (same as in _run_integrated_generation)

        # Determine input type and content
        input_type = self.input_type_var.get()
        custom_script = None
        custom_title = None
        ai_custom_title = None

        if input_type == "topic":
            # For topic-based generation, use the topic as a custom title
            topic = self.topic_var.get().strip()
            if topic:
                ai_custom_title = topic
            else:
                # If no topic provided, let the AI generate based on story type alone (like main.py)
                ai_custom_title = None
        elif input_type == "custom":
            custom_script = self.custom_script_var.get()
        elif input_type == "file":
            script_file = self.script_file_var.get()
            try:
                with open(script_file, 'r', encoding='utf-8') as f:
                    custom_script = f.read()
            except Exception as e:
                raise Exception(f"Could not read script file: {e}")
        else:
            # Default case: generate story based on story type alone (like main.py)
            ai_custom_title = None

        # Use the exact story type from the GUI (matches STORY_TYPE_HASHTAGS keys)
        # No mapping needed - pass the story type exactly as selected in the GUI
        story_type = self.story_type_var.get()

        # Map image style - Use comprehensive image style variable
        image_style = self.image_style_var.get()

        # Map TTS model based on voice API
        voice_api = self.voice_api_var.get()
        if voice_api == "ElevenLabs":
            tts_model = "ElevenLabs"
        elif voice_api == "Voicely":
            tts_model = "Voicely"
        else:
            tts_model = "OpenAI"  # Default

        # Map image model based on image API
        image_api = self.image_api_var.get()
        if image_api == "Together AI Flux":
            image_model = "Together AI Flux"
        elif image_api == "Replicate Flux":
            image_model = "Replicate Flux"
        elif image_api == "FAL AI Flux":
            image_model = "FAL AI Flux"
        else:
            image_model = "Together AI Flux"  # Default

        # Get other settings
        voice_name = self.voice_var.get()
        font_name = self.subtitle_font_var.get()
        font_color = self.font_color_var.get()
        font_size = self.font_size_var.get()
        outline_color = self.outline_color_var.get()
        outline_size = self.outline_size_var.get()
        caption_position = self.caption_position_var.get().lower()
        words_per_caption = self.words_per_caption_var.get()
        if words_per_caption == "Full":
            caption_words = 999
        else:
            caption_words = int(words_per_caption)
        highlight_words = self.word_highlighting_var.get()
        highlight_color = self.highlight_color_var.get()
        highlight_style = self.highlight_style_var.get().lower().replace(" ", "_")

        # Get background music settings - Enhanced implementation like main.py
        bg_music = self.background_music_var.get()
        bg_music_path = None
        if bg_music != "None":
            # Get the music file path directly from the music directory
            script_dir = os.path.dirname(os.path.abspath(__file__))
            music_dir = os.path.join(os.path.dirname(script_dir), "music")
            bg_music_path = os.path.join(music_dir, f"{bg_music}.mp3")

            # Verify the file exists
            if not os.path.exists(bg_music_path):
                print(f"Warning: Music file not found: {bg_music_path}")
                bg_music_path = None

        bg_music_volume = self.music_volume_var.get() / 100.0  # Convert percentage to decimal

        llm_provider = self.script_api_var.get().title()
        end_pause_duration = self.end_pause_duration_var.get()
        aspect_ratio = self.aspect_var.get()
        if aspect_ratio == "9:16":
            orientation = "portrait"
        elif aspect_ratio == "16:9":
            orientation = "landscape"
        else:
            orientation = "square"
        video_quality = self.video_quality_var.get()

        # Create and return the VideoGeneratorThread
        return VideoGeneratorThread(
            story_type=story_type,
            image_style=image_style,
            tts_model=tts_model,
            image_model=image_model,
            voice_name=voice_name,
            font_name=font_name,
            font_color=font_color,
            font_size=font_size,
            outline_color=outline_color,
            outline_size=outline_size,
            caption_position=caption_position,
            caption_words=caption_words,
            callback=self._update_status_from_thread,
            update_progress=self._update_progress_from_thread,
            highlight_words=highlight_words,
            highlight_color=highlight_color,
            custom_script=custom_script,
            custom_title=custom_title,
            bg_music_path=bg_music_path,
            bg_music_volume=bg_music_volume,
            llm_provider=llm_provider,
            ai_custom_title=ai_custom_title,
            end_pause_duration=end_pause_duration,
            orientation=orientation,
            video_quality=video_quality,
            highlight_style=highlight_style
        )

    def _pause_generation(self):
        """Pause the current video generation"""
        if self.current_generation_thread and self.current_generation_thread.is_alive():
            self.current_generation_thread.pause()
            self.status_var.set("Generation paused")
            # Update button states
            self.pause_button.configure(text="RESUME", command=self._resume_generation)

    def _resume_generation(self):
        """Resume the paused video generation"""
        if self.current_generation_thread and self.current_generation_thread.is_alive():
            self.current_generation_thread.resume()
            self.status_var.set("Generation resumed")
            # Update button states
            self.pause_button.configure(text="PAUSE", command=self._pause_generation)

    def _stop_generation(self):
        """Stop the current video generation"""
        if self.current_generation_thread and self.current_generation_thread.is_alive():
            self.current_generation_thread.stop()
            self.status_var.set("Stopping generation...")
            # The monitoring will handle the final cleanup

    def _update_generation_controls(self, is_generating):
        """Update the state of generation control buttons"""
        if is_generating:
            # Disable generate button, enable pause/stop
            self.generate_button.configure(state="disabled")
            self.pause_button.configure(state="normal")
            self.stop_button.configure(state="normal")
        else:
            # Enable generate button, disable pause/stop
            self.generate_button.configure(state="normal")
            self.pause_button.configure(state="disabled")
            self.stop_button.configure(state="disabled")
            # Reset pause button text
            self.pause_button.configure(text="PAUSE", command=self._pause_generation)

    def _log_to_console(self, message):
        """Log a message to the console (placeholder for now)"""
        print(f"[GUI] {message}")

    def _get_widget_fg_color(self):
        """Get the normal foreground color for widgets"""
        return "#FFFFFF" if ctk.get_appearance_mode() == "Dark" else "#000000"

    def _get_disabled_color(self):
        """Get the disabled color for widgets"""
        return "#666666" if ctk.get_appearance_mode() == "Dark" else "#CCCCCC"

    def _show_api_settings(self):
        """Show API settings dialog (placeholder)"""
        messagebox.showinfo("API Settings", "API Settings functionality will be implemented in a future update.\n\nFor now, please configure your API keys in the .env file.")

    def _on_tts_model_change(self, selected_model):
        """Update voice options when TTS model changes"""
        if not IMPORTS_SUCCESSFUL:
            return

        try:
            if selected_model == "OpenAI":
                # OpenAI voices
                voice_options = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
                self.voice_dropdown.configure(values=voice_options)
                self.voice_var.set("alloy")

                # Hide locale filter for OpenAI
                if hasattr(self, 'filter_locale_label'):
                    self.filter_locale_label.grid_remove()
                    self.filter_locale_dropdown.grid_remove()

            elif selected_model == "ElevenLabs":
                # Try to get ElevenLabs voices
                try:
                    from elevenlabs_client import elevenlabs_client

                    # Show loading message
                    self.voice_dropdown.configure(values=["Loading ElevenLabs voices..."])
                    self.voice_var.set("Loading ElevenLabs voices...")
                    self.update()  # Force UI update

                    # Check if API key is available
                    if not elevenlabs_client.api_key:
                        self.voice_dropdown.configure(values=["ElevenLabs API key not set"])
                        self.voice_var.set("ElevenLabs API key not set")
                        messagebox.showwarning("ElevenLabs API Key", "ElevenLabs API key not set. Please add your API key to the .env file.")
                        return

                    # Get voices from ElevenLabs
                    voices = elevenlabs_client.get_voices()
                    if voices:
                        voice_options = [f"{voice['name']}" for voice in voices]
                        self.voice_dropdown.configure(values=voice_options)
                        self.voice_var.set(voice_options[0] if voice_options else "No voices found")
                    else:
                        self.voice_dropdown.configure(values=["No ElevenLabs voices found"])
                        self.voice_var.set("No ElevenLabs voices found")
                        messagebox.showwarning("ElevenLabs Voices", "No voices found. Please check your ElevenLabs API key.")

                except Exception as e:
                    self.voice_dropdown.configure(values=["Error loading ElevenLabs voices"])
                    self.voice_var.set("Error loading ElevenLabs voices")
                    messagebox.showerror("ElevenLabs Error", f"Error loading ElevenLabs voices: {str(e)}")

                # Hide locale filter for ElevenLabs
                if hasattr(self, 'filter_locale_label'):
                    self.filter_locale_label.grid_remove()
                    self.filter_locale_dropdown.grid_remove()

            elif selected_model == "Voicely":
                # Load Voicely/Edge TTS voices from JSON
                try:
                    import json
                    script_dir = os.path.dirname(os.path.abspath(__file__))
                    json_path = os.path.join(script_dir, "src", "english_edge_voices.json")

                    with open(json_path, "r") as f:
                        english_voices = json.load(f)

                    # Create voice options list
                    voice_options = [voice["ShortName"] for voice in english_voices]
                    voice_options.sort()

                    self.voice_dropdown.configure(values=voice_options)
                    self.voice_var.set("en-US-AriaNeural")  # Default voice

                    # Show locale filter for Voicely
                    if hasattr(self, 'filter_locale_label'):
                        self.filter_locale_label.grid()
                        self.filter_locale_dropdown.grid()

                except Exception as e:
                    print(f"Error loading Voicely voices: {e}")
                    # Fallback to default list
                    voice_options = [
                        "en-US-AriaNeural", "en-US-JennyNeural", "en-US-GuyNeural", "en-US-AndrewNeural",
                        "en-GB-SoniaNeural", "en-GB-RyanNeural", "en-AU-NatashaNeural", "en-CA-ClaraNeural"
                    ]
                    self.voice_dropdown.configure(values=voice_options)
                    self.voice_var.set("en-US-AriaNeural")

                    # Show locale filter for Voicely
                    if hasattr(self, 'filter_locale_label'):
                        self.filter_locale_label.grid()
                        self.filter_locale_dropdown.grid()

        except Exception as e:
            print(f"Error in _on_tts_model_change: {e}")

    def _validate_inputs(self):
        """Validate the user inputs"""
        # Basic validation without API key checking for now
        # API key validation will be handled by the VideoGeneratorThread

        # Check FFmpeg availability (still required)
        import subprocess
        try:
            result = subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            if result.returncode != 0:
                messagebox.showerror("Dependency Error", "FFmpeg is required for video rendering but returned an error.\n\nPlease install FFmpeg from https://ffmpeg.org/download.html and add it to your PATH.")
                return False
        except (subprocess.SubprocessError, FileNotFoundError):
            messagebox.showerror("Dependency Error", "FFmpeg is required for video rendering but not found.\n\nPlease install FFmpeg from https://ffmpeg.org/download.html and add it to your PATH.")
            return False

        # Validate input content
        input_type = self.input_type_var.get()

        # Topic is optional - if not provided, AI will generate based on story type alone
        # This matches the behavior in main.py

        if input_type == "custom" and not self.custom_script_var.get().strip():
            messagebox.showerror("Input Error", "Please enter a custom script for your video.")
            return False

        if input_type == "file":
            script_file = self.script_file_var.get()
            if not script_file:
                messagebox.showerror("Input Error", "Please select a script file.")
                return False
            if not os.path.exists(script_file):
                messagebox.showerror("Input Error", f"The script file '{script_file}' does not exist.")
                return False

        # Duration and scenes validation removed since those fields were removed

        # Validate style, transition, and aspect ratio - Updated for comprehensive image styles
        valid_styles = [
            "photorealistic", "cinematic", "anime", "comic-book", "pixar-art",
            "dark-aesthetic", "neon-cyberpunk", "minimalist", "film-noir",
            "retro-80s", "vaporwave", "cottagecore", "hyperrealistic",
            "flat-design", "3d-cartoon", "pastel-dreamscape", "fantasy-vibrant",
            "nostalgic-filter", "vhs-aesthetic", "y2k", "god-anime-vine", "ghibli"
        ]
        if self.image_style_var.get() not in valid_styles:
            messagebox.showerror("Input Error", "Invalid image style selected.")
            return False

        # Transition validation removed as transition effects dropdown was removed

        if self.aspect_var.get() not in ["16:9", "9:16", "1:1", "4:3", "21:9"]:
            messagebox.showerror("Input Error", "Invalid aspect ratio selected.")
            return False

        return True

    def _toggle_highlighting_controls(self):
        """Toggle highlighting controls based on word highlighting state"""
        if self.word_highlighting_var.get():
            state = "normal"
            fg_color = self._get_widget_fg_color()  # Normal background color
        else:
            state = "disabled"
            fg_color = self._get_disabled_color()  # Disabled background color

        # Set state for labels and radio buttons
        self.highlight_color_label.configure(state=state)
        self.highlight_style_label.configure(state=state)
        self.text_color_radio.configure(state=state)
        self.background_radio.configure(state=state)

        # For Frame, set visual cue with color change but don't use state parameter
        # Instead, enable/disable individual children
        self.highlight_color_preview.configure(state=state)
        self.highlight_color_button.configure(state=state)
        self.highlight_color_hex.configure(state=state)

    def _toggle_voice_settings(self, *args):
        """Enable or disable voice settings based on voice API selection"""
        if self.voice_api_var.get() == "disabled":
            # Disable all voice settings components
            self.voice_label.configure(state="disabled")
            self.voice_dropdown.configure(state="disabled")
            self.preview_voice_button.configure(state="disabled")
            self.refresh_voice_button.configure(state="disabled")
            self.speech_rate_label.configure(state="disabled")
            self.speech_rate_slider.configure(state="disabled")
            self.speech_rate_value_label.configure(state="disabled")

            # Also disable filter by location
            self.filter_locale_label.configure(state="disabled")
            self.filter_locale_dropdown.configure(state="disabled")
        else:
            # Enable all voice settings components
            self.voice_label.configure(state="normal")
            self.voice_dropdown.configure(state="normal")
            self.preview_voice_button.configure(state="normal")
            self.refresh_voice_button.configure(state="normal")
            self.speech_rate_label.configure(state="normal")
            self.speech_rate_slider.configure(state="normal")
            self.speech_rate_value_label.configure(state="normal")

            # Also enable filter by location
            self.filter_locale_label.configure(state="normal")
            self.filter_locale_dropdown.configure(state="normal")

            # Update voice options based on selected API
            self._update_voice_options_for_api()

    def _update_voice_options_for_api(self):
        """Update voice dropdown options based on selected API"""
        voice_api = self.voice_api_var.get()

        if voice_api == "elevenlabs":
            voices = ["Rachel", "Domi", "Bella", "Antoni", "Josh", "Arnold", "Adam", "Sam"]
        elif voice_api == "coqui_tts":
            # Check Python compatibility
            import sys
            python_version = sys.version_info
            if python_version >= (3, 13):
                voices = ["❌ Python 3.13+ not supported"]
                self.status_var.set("⚠️ Coqui TTS requires Python 3.10-3.12. Using local TTS fallback.")
                # Automatically switch to local_tts
                self.voice_api_var.set("local_tts")
                self._update_voice_options_for_api()  # Recursively update
                return
            else:
                voices = ["default", "female", "male", "jenny", "ljspeech", "fast", "quality"]
        elif voice_api == "local_tts":
            voices = ["default", "female", "male"]
        elif voice_api == "voicely":
            voices = ["NatashaNeutral", "BrianNeural", "EmmaNeural"]
        else:
            voices = ["default"]

        # Update voice dropdown
        self.voice_dropdown.configure(values=voices)

        # Set default voice for the API
        if voices and self.voice_var.get() not in voices:
            self.voice_var.set(voices[0])

    def _save_profile(self):
        """Save the current settings as a profile"""
        profile_name = self.current_profile_var.get()

        if profile_name == "Default":
            # Ask for a new profile name
            self._new_profile()
            return

        # Create a dictionary of all the settings
        settings = {
            # Video Settings
            "aspect": self.aspect_var.get(),
            "bg_music": self.background_music_var.get(),
            "video_quality": self.video_quality_var.get(),
            "music_volume": self.music_volume_var.get(),
            "end_pause": self.end_pause_duration_var.get(),
            "style": self.image_style_var.get(),  # Use comprehensive image style
            "transition": self.transition_var.get(),
            "effects": self.effects_var.get(),

            # Voice Settings
            "voice": self.voice_var.get(),
            "filter_locale": self.filter_locale_var.get(),
            "speech_rate": self.speech_rate_var.get(),

            # Subtitle Settings
            "subtitle_font": self.subtitle_font_var.get(),
            "font_color": self.font_color_var.get(),
            "font_size": self.font_size_var.get(),
            "outline_color": self.outline_color_var.get(),
            "outline_size": self.outline_size_var.get(),
            "caption_position": self.caption_position_var.get(),
            "words_per_caption": self.words_per_caption_var.get(),
            "word_highlighting": self.word_highlighting_var.get(),
            "highlight_color": self.highlight_color_var.get(),
            "highlight_style": self.highlight_style_var.get()
        }

        # Save the settings
        self.profiles[profile_name] = settings

        # Save profiles to file
        self._save_profiles_to_file()

        # Show confirmation
        self.status_var.set(f"Profile '{profile_name}' saved successfully")
        self.after(2000, lambda: self.status_var.set("Generation Status"))

    def _new_profile(self):
        """Create a new profile"""
        from tkinter import simpledialog

        # Ask for profile name
        profile_name = simpledialog.askstring("New Profile", "Enter profile name:")

        if not profile_name:
            return

        # Check if profile name already exists
        if profile_name in self.profiles:
            messagebox.showerror("Error", f"Profile '{profile_name}' already exists. Choose a different name.")
            return

        # Add profile to dropdown
        profile_values = list(self.profile_dropdown.cget("values"))
        profile_values.append(profile_name)
        self.profile_dropdown.configure(values=profile_values)

        # Set as current profile
        self.current_profile_var.set(profile_name)

        # Save current settings to this profile
        self._save_profile()

    def _delete_profile(self):
        """Delete the selected profile"""
        profile_name = self.current_profile_var.get()

        if profile_name == "Default":
            messagebox.showerror("Error", "Cannot delete the Default profile.")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete the profile '{profile_name}'?"):
            return

        # Remove from profiles dictionary
        if profile_name in self.profiles:
            del self.profiles[profile_name]

        # Remove from dropdown
        profile_values = list(self.profile_dropdown.cget("values"))
        profile_values.remove(profile_name)
        self.profile_dropdown.configure(values=profile_values)

        # Set current to Default
        self.current_profile_var.set("Default")

        # Save changes
        self._save_profiles_to_file()

        # Show confirmation
        self.status_var.set(f"Profile '{profile_name}' deleted")
        self.after(2000, lambda: self.status_var.set("Generation Status"))

    def _load_profile(self, profile_name=None):
        """Load settings from the selected profile"""
        if profile_name is None:
            profile_name = self.current_profile_var.get()

        if profile_name == "Default" or profile_name not in self.profiles:
            return

        settings = self.profiles[profile_name]

        # Apply all the settings
        # Video Settings
        self.aspect_var.set(settings.get("aspect", "16:9"))
        self.background_music_var.set(settings.get("bg_music", "None"))
        self.video_quality_var.set(settings.get("video_quality", "720p"))
        self.music_volume_var.set(settings.get("music_volume", 20))
        self.end_pause_duration_var.set(settings.get("end_pause", 3.5))
        # Handle both old style_var and new image_style_var for backward compatibility
        style_value = settings.get("style", "cinematic")
        if hasattr(self, 'image_style_var'):
            self.image_style_var.set(style_value)
        if hasattr(self, 'style_var'):
            self.style_var.set(style_value)
        self.transition_var.set(settings.get("transition", "fade"))
        self.effects_var.set(settings.get("effects", "None"))

        # Voice Settings
        self.voice_var.set(settings.get("voice", "NatashaNeutral"))
        self.filter_locale_var.set(settings.get("filter_locale", "All"))
        self.speech_rate_var.set(settings.get("speech_rate", 100))

        # Subtitle Settings
        self.subtitle_font_var.set(settings.get("subtitle_font", "TitanOne"))
        self.font_color_var.set(settings.get("font_color", "#FFFFFF"))
        self.font_size_var.set(settings.get("font_size", 50))
        self.outline_color_var.set(settings.get("outline_color", "#000000"))
        self.outline_size_var.set(settings.get("outline_size", 3))
        self.caption_position_var.set(settings.get("caption_position", "Bottom"))
        self.words_per_caption_var.set(settings.get("words_per_caption", "5"))
        self.word_highlighting_var.set(settings.get("word_highlighting", True))
        self.highlight_color_var.set(settings.get("highlight_color", "#FFFF00"))
        self.highlight_style_var.set(settings.get("highlight_style", "Text Color"))

        # Update the UI
        self._toggle_highlighting_controls()

        # Show confirmation
        self.status_var.set(f"Profile '{profile_name}' loaded")
        self.after(2000, lambda: self.status_var.set("Generation Status"))

    def _save_profiles_to_file(self):
        """Save profiles to a JSON file"""
        try:
            config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config")
            os.makedirs(config_dir, exist_ok=True)

            profiles_file = os.path.join(config_dir, "profiles.json")

            with open(profiles_file, 'w') as f:
                json.dump(self.profiles, f, indent=4)

        except Exception as e:
            print(f"Error saving profiles: {e}")
            messagebox.showerror("Error", f"Failed to save profiles: {e}")

    def _load_profiles_from_file(self):
        """Load profiles from a JSON file"""
        try:
            config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config")
            profiles_file = os.path.join(config_dir, "profiles.json")

            if os.path.exists(profiles_file):
                with open(profiles_file, 'r') as f:
                    self.profiles = json.load(f)

                # Update dropdown with profile names
                profile_values = ["Default"] + list(self.profiles.keys())
                self.profile_dropdown.configure(values=profile_values)

        except Exception as e:
            print(f"Error loading profiles: {e}")

            # Create empty profiles
            self.profiles = {}

    def _set_aspect_ratio(self, aspect_text):
        """Set the aspect ratio from the dropdown text"""
        if aspect_text == "Portrait (9:16)":
            self.aspect_var.set("9:16")
        elif aspect_text == "Landscape (16:9)":
            self.aspect_var.set("16:9")
        elif aspect_text == "Square (1:1)":
            self.aspect_var.set("1:1")

    def _get_aspect_display(self, aspect_value):
        """Get display text for the aspect ratio dropdown"""
        if aspect_value == "9:16":
            return "Portrait (9:16)"
        elif aspect_value == "16:9":
            return "Landscape (16:9)"
        elif aspect_value == "1:1":
            return "Square (1:1)"
        return "Landscape (16:9)"  # Default

    def _preview_subtitle_font(self):
        """Preview the selected subtitle font"""
        font_name = self.subtitle_font_var.get()
        font_color = self.font_color_var.get()

        # Create a preview dialog
        preview_window = ctk.CTkToplevel(self)
        preview_window.title(f"Font Preview: {font_name}")
        preview_window.geometry("400x200")
        preview_window.resizable(False, False)
        preview_window.grab_set()  # Make the window modal

        # Add a frame with the background color of the application
        preview_frame = ctk.CTkFrame(preview_window)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create a label with the sample text
        sample_text = "The quick brown fox jumps over the lazy dog."

        # We're using a tkinter label as CTkLabel doesn't support custom fonts directly
        # For a real implementation, you'd need to handle font loading appropriately
        sample_label = tk.Label(
            preview_frame,
            text=sample_text,
            font=(font_name, 16),
            fg=font_color,
            bg=preview_frame.cget("fg_color")[1 if ctk.get_appearance_mode() == "Dark" else 0]
        )
        sample_label.pack(pady=20)

        # Add a note about actual fonts
        note_label = ctk.CTkLabel(
            preview_frame,
            text="Note: Actual appearance may vary in the final video.",
            font=ctk.CTkFont(size=10),
            text_color="#888888"
        )
        note_label.pack(pady=10)

        # Close button
        close_button = ctk.CTkButton(preview_frame, text="Close", command=preview_window.destroy)
        close_button.pack(pady=10)

    def _upload_bulk_scripts(self):
        """Open file dialog to select multiple script files for bulk generation"""
        filenames = filedialog.askopenfilenames(
            title="Select Multiple Script Files",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )

        if filenames:
            # Set the selected file count in the status
            self.bulk_scripts_var.set(f"Selected: {len(filenames)} script files")

            # Store the file paths
            self.bulk_scripts_files = filenames

            # Update batch count to match the number of selected files
            self.batch_count_var.set(str(len(filenames)))

            # Enable batch generation if it's not already
            if not self.enable_batch_var.get():
                self.enable_batch_var.set(True)
                self._toggle_batch_generation()

            # Update status
            self.status_var.set(f"Loaded {len(filenames)} script files for bulk generation")
            self.after(2000, lambda: self.status_var.set("Generation Status"))

    def _set_duration(self, value):
        """Set the duration based on the selected value"""
        if value == "30 seconds":
            self.duration_var.set("30")
        elif value == "1 minute":
            self.duration_var.set("60")
        elif value == "2 minutes":
            self.duration_var.set("120")
        elif value == "6 minutes":
            self.duration_var.set("360")
        elif value == "10 minutes":
            self.duration_var.set("600")
        elif value == "15 minutes":
            self.duration_var.set("900")
        else:
            # Custom duration
            from tkinter import simpledialog
            custom_minutes = simpledialog.askstring("Custom Duration", "Enter duration in minutes:")
            if custom_minutes and custom_minutes.replace('.', '').isdigit():
                # Convert minutes to seconds
                duration_seconds = int(float(custom_minutes) * 60)
                self.duration_var.set(str(duration_seconds))

                # Temporarily show the custom value but don't permanently add it
                custom_display = f"{custom_minutes} minutes"
                self.duration_dropdown.set(custom_display)
            elif custom_minutes:
                messagebox.showerror("Input Error", "Please enter a valid number (minutes only).")
            else:
                # User cancelled, reset to a default value
                self.duration_dropdown.set("1 minute")
                self.duration_var.set("60")

    def _open_video_folder(self):
        """Open the folder containing the last generated video"""
        if self.last_generated_video_path and os.path.exists(self.last_generated_video_path):
            import subprocess
            import platform

            # Get the folder containing the video
            folder_path = os.path.dirname(self.last_generated_video_path)

            try:
                if platform.system() == "Windows":
                    os.startfile(folder_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", folder_path])
                else:  # Linux
                    subprocess.run(["xdg-open", folder_path])

                self.status_var.set(f"Opened folder: {os.path.basename(folder_path)}")
                self.after(2000, lambda: self.status_var.set("Generation Status"))
            except Exception as e:
                messagebox.showerror("Error", f"Could not open folder: {e}")
        else:
            messagebox.showinfo("No Video", "No video has been generated yet or the file no longer exists.")

    def _toggle_subtitle_settings(self):
        """Toggle subtitle settings based on enable subtitles checkbox"""
        if self.enable_subtitles_var.get():
            state = "normal"
        else:
            state = "disabled"

        # Enable/disable subtitle font controls if they exist
        if hasattr(self, 'subtitle_font_label'):
            self.subtitle_font_label.configure(state=state)
        if hasattr(self, 'subtitle_font_dropdown'):
            self.subtitle_font_dropdown.configure(state=state)
        if hasattr(self, 'preview_font_button'):
            self.preview_font_button.configure(state=state)

# Main function
def main():
    """Run the GUI application"""
    app = OneClickVideoGUI()
    app.mainloop()

if __name__ == "__main__":
    main()
