#!/usr/bin/env python3
"""
Test script for the Modern 1ClickVideo UI
This script tests the UI without requiring authentication
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all required imports work"""
    try:
        print("Testing imports...")

        # Test basic imports
        from ttkthemes import ThemedTk
        print("✓ ttkthemes imported successfully")

        # Test main imports
        from main import (
            VideoGeneratorThread, switch_ai_provider,
            AI_PROVIDERS, TTS_PROVIDERS, get_available_fonts,
            APP_NAME, APP_VERSION, DEFAULT_FONT, script_dir
        )
        print("✓ main.py imports successful")

        # Test voice preview
        import voice_preview
        print("✓ voice_preview imported successfully")

        # Test voice selection widget
        from voice_selection_widget import VoiceSelectionWidget
        print("✓ VoiceSelectionWidget imported successfully")

        # Test auth (this might fail if not configured)
        try:
            from auth import show_login_dialog, get_current_user_email
            print("✓ auth module imported successfully")
        except Exception as e:
            print(f"⚠ auth module import warning: {e}")

        # Test modern UI
        from modern_ui import ModernApp, ModernColors, ModernButton, ModernCard
        print("✓ modern_ui components imported successfully")

        return True

    except Exception as e:
        print(f"✗ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_creation():
    """Test creating the modern UI without authentication"""
    try:
        print("\nTesting UI creation...")

        from ttkthemes import ThemedTk
        from modern_ui import ModernApp

        # Create root window
        root = ThemedTk(theme="arc")
        root.title("Modern UI Test")
        root.geometry("800x600")

        # Create app (this should work now)
        try:
            app = ModernApp(root)
            print("✓ ModernApp created successfully")

            # Show window briefly
            root.update()
            root.after(3000, root.destroy)  # Close after 3 seconds
            root.mainloop()

            return True

        except Exception as e:
            print(f"⚠ ModernApp creation failed: {e}")
            import traceback
            traceback.print_exc()
            root.destroy()
            return False

    except Exception as e:
        print(f"✗ UI creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_components():
    """Test individual UI components"""
    try:
        print("\nTesting individual components...")

        from ttkthemes import ThemedTk
        from modern_ui import ModernColors, ModernButton, ModernCard

        # Create test window
        root = ThemedTk(theme="arc")
        root.title("Component Test")
        root.geometry("400x300")
        root.configure(bg=ModernColors.BG_PRIMARY)

        # Test ModernCard
        card = ModernCard(root, title="Test Card")
        card.pack(fill=tk.X, padx=20, pady=20)
        print("✓ ModernCard created successfully")

        # Test ModernButton
        button = ModernButton(
            card,
            text="Test Button",
            command=lambda: print("Button clicked!"),
            style="primary"
        )
        button.pack(pady=10)
        print("✓ ModernButton created successfully")

        # Show window briefly
        root.update()
        root.after(2000, root.destroy)  # Close after 2 seconds
        root.mainloop()

        return True

    except Exception as e:
        print(f"✗ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Modern UI Test Suite")
    print("=" * 50)

    # Test imports
    imports_ok = test_imports()

    if imports_ok:
        print("\n" + "=" * 30)
        print("All imports successful!")
        print("=" * 30)

        # Test components
        components_ok = test_components()

        if components_ok:
            print("\n" + "=" * 30)
            print("Component test successful!")
            print("=" * 30)

            # Try full UI (might fail due to auth)
            print("\nAttempting full UI test...")
            ui_ok = test_ui_creation()

            if ui_ok:
                print("\n" + "=" * 30)
                print("✅ ALL TESTS PASSED!")
                print("Modern UI is ready to use!")
                print("=" * 30)
            else:
                print("\n" + "=" * 30)
                print("⚠ UI test failed (likely auth required)")
                print("But components work - UI should be functional")
                print("=" * 30)
        else:
            print("\n" + "=" * 30)
            print("❌ Component tests failed")
            print("=" * 30)
    else:
        print("\n" + "=" * 30)
        print("❌ Import tests failed")
        print("Please check dependencies")
        print("=" * 30)

if __name__ == "__main__":
    main()
