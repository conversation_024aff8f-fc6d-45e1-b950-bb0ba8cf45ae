"""
Utility script to check ElevenLabs API status and list available voices.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from src
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
sys.path.append(script_dir)  # Also add the current directory

# Load environment variables
dotenv_path = os.path.join(parent_dir, ".env")
load_dotenv(dotenv_path)

def check_elevenlabs_api():
    """Check if ElevenLabs API key is set and working"""
    try:
        # Import the ElevenLabs client
        from elevenlabs_client import elevenlabs_client

        # Check if API key is set
        if not elevenlabs_client.is_available:
            print("ElevenLabs API key not set. Please add your API key to the .env file.")
            return False, "API key not set"

        # Try to get voices to verify API key is working
        voices = elevenlabs_client.get_voices()

        if not voices:
            print("ElevenLabs API key is set but no voices were found. The API key may be invalid.")
            return False, "No voices found"

        # API key is working
        print(f"ElevenLabs API key is valid. Found {len(voices)} voices.")

        # Print voice details
        print("\nAvailable voices:")
        for voice in voices:
            print(f"- {voice['name']} ({voice['category']})")

        return True, f"Found {len(voices)} voices"

    except Exception as e:
        print(f"Error checking ElevenLabs API: {str(e)}")
        return False, f"Error: {str(e)}"

if __name__ == "__main__":
    # Check ElevenLabs API status
    status, message = check_elevenlabs_api()

    # Print status
    print(f"\nElevenLabs API Status: {'OK' if status else 'ERROR'}")
    print(f"Message: {message}")

    # Exit with appropriate code
    sys.exit(0 if status else 1)
