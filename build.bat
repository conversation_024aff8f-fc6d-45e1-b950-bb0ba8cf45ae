@echo off
setlocal EnableDelayedExpansion
title Building Modern 1ClickVideo v6.5

echo =====================================================
echo   Building Secure Distribution for Modern 1ClickVideo v6.5
echo =====================================================
echo.
echo This window will stay open after completion. Please do not close it.
echo.
echo Building with Modern UI Components and Contemporary Design
echo.

:: Check and create virtual environment if needed
echo Checking for virtual environment...
if not exist "venv" (
    echo Virtual environment not found. Creating new virtual environment...
    python -m venv venv || (
        echo ERROR: Failed to create virtual environment.
        echo Make sure Python is installed and accessible.
        echo.
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
)

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat || (
    echo ERROR: Failed to activate virtual environment.
    echo Attempting to recreate virtual environment...
    rmdir /s /q venv 2>nul
    python -m venv venv || (
        echo ERROR: Failed to recreate virtual environment.
        echo Running without virtual environment...
        goto :skip_venv
    )
    call venv\Scripts\activate.bat || (
        echo ERROR: Still cannot activate virtual environment.
        echo Running without virtual environment...
        goto :skip_venv
    )
)
echo Virtual environment activated successfully.

:skip_venv
echo.

:: Install required packages for build process
echo Installing required packages for build process...
pip install --upgrade pip 2>nul
pip install pyminifier 2>nul || (
    echo Failed to install pyminifier. Using built-in obfuscation method...
)

:: Ensure critical dependencies are available
echo Checking critical dependencies...
python -c "import base64, zlib, random, string, os" 2>nul || (
    echo ERROR: Critical Python modules not available.
    echo Please ensure Python installation is complete.
    pause
    exit /b 1
)

:: Create secure distribution directory
if exist "Secure_Distribution" rmdir /s /q "Secure_Distribution"
mkdir "Secure_Distribution"
mkdir "Secure_Distribution\src"

:: Create all subdirectories in src
mkdir "Secure_Distribution\src\services"
mkdir "Secure_Distribution\src\assets"
mkdir "Secure_Distribution\src\data"
mkdir "Secure_Distribution\src\test_audio"
mkdir "Secure_Distribution\src\test_output"

:: Create external resource directories
mkdir "Secure_Distribution\resources"
mkdir "Secure_Distribution\font"
mkdir "Secure_Distribution\data"
mkdir "Secure_Distribution\assets"

:: Copy resources and config files
echo Copying resource files and directories...
if exist "resources" xcopy "resources" "Secure_Distribution\resources\" /E /H /C /I /y
if exist "font" xcopy "font" "Secure_Distribution\font\" /E /H /C /I /y
if exist "assets" xcopy "assets" "Secure_Distribution\assets\" /E /H /C /I /y
if exist "data" xcopy "data" "Secure_Distribution\data\" /E /H /C /I /y

:: Copy src subdirectory assets
if exist "src\assets" xcopy "src\assets" "Secure_Distribution\src\assets\" /E /H /C /I /y
if exist "src\test_audio" xcopy "src\test_audio" "Secure_Distribution\src\test_audio\" /E /H /C /I /y
if exist "src\test_output" xcopy "src\test_output" "Secure_Distribution\src\test_output\" /E /H /C /I /y

:: Copy JSON and other non-Python files from src
for %%f in (src\*.json) do (
    echo Copying %%f...
    copy "%%f" "Secure_Distribution\%%f"
)

:: Copy other important files
if exist ".env" copy ".env" "Secure_Distribution\"
if exist ".env.example" copy ".env.example" "Secure_Distribution\"
if exist "README.md" copy "README.md" "Secure_Distribution\"
if exist "MODERN_UI_README.md" copy "MODERN_UI_README.md" "Secure_Distribution\"
if exist "requirements.txt" copy "requirements.txt" "Secure_Distribution\"
if exist "config.json" copy "config.json" "Secure_Distribution\"

:: Copy modern UI launch script and related files
if exist "launch_modern_ui.py" copy "launch_modern_ui.py" "Secure_Distribution\"
if exist "demo_modern_ui.py" copy "demo_modern_ui.py" "Secure_Distribution\"
if exist "test_modern_ui.py" copy "test_modern_ui.py" "Secure_Distribution\"
if exist "run_modern_ui.bat" copy "run_modern_ui.bat" "Secure_Distribution\"
if exist "test_modern_ui_build.py" copy "test_modern_ui_build.py" "Secure_Distribution\"

:: Create __init__.py files to make proper packages
echo # Simple Obfuscator Runtime > "Secure_Distribution\src\__init__.py"
echo # Make src a proper package that can be imported > "Secure_Distribution\src\__init__.py"

:: Process Python files
echo.
echo Processing Python files...

:: Check if obfuscator.py exists
if not exist "obfuscator.py" (
    echo ERROR: obfuscator.py not found. Please ensure the obfuscator.py file exists.
    echo You can create it manually or copy it from the project repository.
    pause
    exit /b 1
) else (
    echo Found obfuscator.py script.
)

:: Use the obfuscator.py file
echo Using obfuscator.py file...

:: Process all Python files recursively with proper path handling
echo.
echo Processing all Python files in src directory...

:: Create __init__.py files for all subdirectories to make proper packages
echo # Services module > "Secure_Distribution\src\services\__init__.py"
echo # Assets module > "Secure_Distribution\src\assets\__init__.py"
echo # Data module > "Secure_Distribution\src\data\__init__.py"
echo # Test audio module > "Secure_Distribution\src\test_audio\__init__.py"
echo # Test output module > "Secure_Distribution\src\test_output\__init__.py"

:: Test obfuscator script first
echo Testing obfuscation script...
python obfuscator.py --help >nul 2>&1
if errorlevel 1 (
    echo WARNING: Obfuscation script test failed, but continuing with build...
    echo Checking Python environment...
    python -c "print('Python is working')" || (
        echo ERROR: Python is not working properly.
        pause
        exit /b 1
    )
) else (
    echo Obfuscation script is working correctly.
)

:: Process root Python files with enhanced error handling
echo.
echo Processing Python files in src directory...
for %%f in (src\*.py) do (
    echo Processing: %%f
    if "%%~nxf" NEQ "__init__.py" (
        python obfuscator.py "%%f" "Secure_Distribution\%%f" >nul 2>&1
        if errorlevel 1 (
            echo Obfuscation failed for %%f, copying original file...
            if not exist "Secure_Distribution\src" mkdir "Secure_Distribution\src"
            copy "%%f" "Secure_Distribution\%%f" >nul 2>&1 || (
                echo ERROR: Failed to copy %%f
            )
        ) else (
            echo Successfully processed: %%f
        )
    ) else (
        echo Copying __init__.py file: %%f
        copy "%%f" "Secure_Distribution\%%f" >nul 2>&1
    )
)

:: Process Python files in the root directory
echo.
echo Processing Python files in the root directory...
for %%f in (*.py) do (
    echo Processing root file: %%f
    python obfuscator.py "%%f" "Secure_Distribution\%%f" >nul 2>&1
    if errorlevel 1 (
        echo Obfuscation failed for %%f, copying original file...
        copy "%%f" "Secure_Distribution\%%f" >nul 2>&1 || (
            echo ERROR: Failed to copy %%f
        )
    ) else (
        echo Successfully processed: %%f
    )
)

:: Process services directory Python files
echo.
echo Processing services directory Python files...
if exist "src\services" (
    for %%f in (src\services\*.py) do (
        echo Processing: %%f
        if "%%~nxf" NEQ "__init__.py" (
            python obfuscator.py "%%f" "Secure_Distribution\%%f" >nul 2>&1
            if errorlevel 1 (
                echo Obfuscation failed for %%f, copying original file...
                if not exist "Secure_Distribution\src\services" mkdir "Secure_Distribution\src\services"
                copy "%%f" "Secure_Distribution\%%f" >nul 2>&1 || (
                    echo ERROR: Failed to copy %%f
                )
            ) else (
                echo Successfully processed: %%f
            )
        ) else (
            echo Copying __init__.py file: %%f
            copy "%%f" "Secure_Distribution\%%f" >nul 2>&1
        )
    )
) else (
    echo WARNING: services directory not found in src. This may cause import errors.
)

:: Process any other Python files in subdirectories
echo.
echo Processing other subdirectory Python files...
for /r "src" %%f in (*.py) do (
    :: Skip files in root src and services directories (already processed)
    set "filepath=%%f"
    set "dirpath=%%~dpf"
    set "dirpath=!dirpath:~0,-1!"

    if /i "!dirpath!" NEQ "%CD%\src" if /i "!dirpath!" NEQ "%CD%\src\services" (
        echo Processing subdirectory file: %%f
        set "relpath=%%f"
        set "relpath=!relpath:%CD%\=!"

        :: Create target directory structure
        set "targetdir=Secure_Distribution\!relpath!"
        for %%d in ("!targetdir!") do set "targetdir=%%~dpd"
        if not exist "!targetdir!" mkdir "!targetdir!" 2>nul

        if "%%~nxf" NEQ "__init__.py" (
            python obfuscator.py "%%f" "Secure_Distribution\!relpath!" >nul 2>&1
            if errorlevel 1 (
                echo Obfuscation failed for %%f, copying original file...
                copy "%%f" "Secure_Distribution\!relpath!" >nul 2>&1 || (
                    echo ERROR: Failed to copy %%f
                )
            ) else (
                echo Successfully processed: %%f
            )
        ) else (
            echo Copying __init__.py file: %%f
            copy "%%f" "Secure_Distribution\!relpath!" >nul 2>&1
        )
    )
)

:: Create main runner script with venv check
echo Creating run.bat...
echo @echo off > "Secure_Distribution\run.bat"
echo setlocal EnableDelayedExpansion >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo echo ======================================================= >> "Secure_Distribution\run.bat"
echo echo   Running Modern 1ClickVideo v6.5 >> "Secure_Distribution\run.bat"
echo echo ======================================================= >> "Secure_Distribution\run.bat"
echo echo. >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo :: Check if venv exists and activate it >> "Secure_Distribution\run.bat"
echo if exist "venv\Scripts\activate.bat" ( >> "Secure_Distribution\run.bat"
echo     echo Activating virtual environment... >> "Secure_Distribution\run.bat"
echo     call venv\Scripts\activate.bat >> "Secure_Distribution\run.bat"
echo     echo Virtual environment activated successfully. >> "Secure_Distribution\run.bat"
echo ) else ( >> "Secure_Distribution\run.bat"
echo     echo No virtual environment found. Running with system Python... >> "Secure_Distribution\run.bat"
echo ) >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo :: Create default config file if it doesn't exist >> "Secure_Distribution\run.bat"
echo if not exist "config.json" ( >> "Secure_Distribution\run.bat"
echo     echo Creating default config file... >> "Secure_Distribution\run.bat"
echo     echo { > "config.json" >> "Secure_Distribution\run.bat"
echo     echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo   "whatsapp_number": "+8801734690909", >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo   "tts": { >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo     "speech_rate": 1.0 >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo   } >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo } >> "config.json" >> "Secure_Distribution\run.bat"
echo ) >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo :: Check for MoviePy installation >> "Secure_Distribution\run.bat"
echo python -c "import moviepy" 2>nul >> "Secure_Distribution\run.bat"
echo if errorlevel 1 ( >> "Secure_Distribution\run.bat"
echo     echo MoviePy package not found. Installing it now... >> "Secure_Distribution\run.bat"
echo     pip install moviepy >> "Secure_Distribution\run.bat"
echo     if errorlevel 1 ( >> "Secure_Distribution\run.bat"
echo         echo Failed to install MoviePy. Trying alternate method... >> "Secure_Distribution\run.bat"
echo         pip install --upgrade pip >> "Secure_Distribution\run.bat"
echo         pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1 >> "Secure_Distribution\run.bat"
echo         if errorlevel 1 ( >> "Secure_Distribution\run.bat"
echo             echo ERROR: Could not install MoviePy. Please install it manually by running: >> "Secure_Distribution\run.bat"
echo             echo pip install moviepy >> "Secure_Distribution\run.bat"
echo             pause >> "Secure_Distribution\run.bat"
echo         ) else ( >> "Secure_Distribution\run.bat"
echo             echo MoviePy installed successfully! >> "Secure_Distribution\run.bat"
echo         ) >> "Secure_Distribution\run.bat"
echo     ) else ( >> "Secure_Distribution\run.bat"
echo         echo MoviePy installed successfully! >> "Secure_Distribution\run.bat"
echo     ) >> "Secure_Distribution\run.bat"
echo ) >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo echo Setting up Python path... >> "Secure_Distribution\run.bat"
echo set PYTHONPATH=%CD% >> "Secure_Distribution\run.bat"
echo echo Starting Modern UI application... >> "Secure_Distribution\run.bat"
echo python launch_modern_ui.py >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo echo Application closed. Press any key to exit... >> "Secure_Distribution\run.bat"
echo pause >> "Secure_Distribution\run.bat"

:: Create a proper config.json file with all required sections
echo Creating config.json...
echo { > "Secure_Distribution\config.json"
echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "Secure_Distribution\config.json"
echo   "whatsapp_number": "+8801734690909", >> "Secure_Distribution\config.json"
echo   "tts": { >> "Secure_Distribution\config.json"
echo     "speech_rate": 1.0 >> "Secure_Distribution\config.json"
echo   } >> "Secure_Distribution\config.json"
echo } >> "Secure_Distribution\config.json"

:: Create advanced installation script that checks Python version and sets up venv
echo Creating install.bat...
echo @echo off > "Secure_Distribution\install.bat"
echo setlocal EnableDelayedExpansion >> "Secure_Distribution\install.bat"
echo title Installing Modern 1ClickVideo v6.5 >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo   Installing Modern 1ClickVideo v6.5 >> "Secure_Distribution\install.bat"
echo echo   with Contemporary UI and Modern Design >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo :: Check if Python 3.12.8 is installed >> "Secure_Distribution\install.bat"
echo echo Checking Python installation... >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo python --version 2>nul | findstr "3.12.8" > nul >> "Secure_Distribution\install.bat"
echo if errorlevel 1 ( >> "Secure_Distribution\install.bat"
echo     echo Python 3.12.8 is not installed or not in PATH. >> "Secure_Distribution\install.bat"
echo     echo Downloading Python 3.12.8 installer... >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo     :: Download Python 3.12.8 installer using PowerShell >> "Secure_Distribution\install.bat"
echo     powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.12.8/python-3.12.8-amd64.exe' -OutFile 'python-3.12.8-amd64.exe'}" >> "Secure_Distribution\install.bat"
echo     if not exist "python-3.12.8-amd64.exe" ( >> "Secure_Distribution\install.bat"
echo         echo Failed to download Python installer. >> "Secure_Distribution\install.bat"
echo         echo Please manually install Python 3.12.8 from https://www.python.org/downloads/ >> "Secure_Distribution\install.bat"
echo         echo Then run this install script again. >> "Secure_Distribution\install.bat"
echo         pause >> "Secure_Distribution\install.bat"
echo         exit /b 1 >> "Secure_Distribution\install.bat"
echo     ) >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo     echo Installing Python 3.12.8... >> "Secure_Distribution\install.bat"
echo     echo Please complete the installation when the installer opens. >> "Secure_Distribution\install.bat"
echo     echo IMPORTANT: Check "Add Python to PATH" during installation. >> "Secure_Distribution\install.bat"
echo     python-3.12.8-amd64.exe /passive InstallAllUsers=1 PrependPath=1 >> "Secure_Distribution\install.bat"
echo     echo. >> "Secure_Distribution\install.bat"
echo     echo Waiting for Python installation to complete... >> "Secure_Distribution\install.bat"
echo     timeout /t 30 >> "Secure_Distribution\install.bat"
echo     del python-3.12.8-amd64.exe >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo     :: Verify Python was installed correctly >> "Secure_Distribution\install.bat"
echo     python --version 2>nul | findstr "3.12" > nul >> "Secure_Distribution\install.bat"
echo     if errorlevel 1 ( >> "Secure_Distribution\install.bat"
echo         echo. >> "Secure_Distribution\install.bat"
echo         echo ERROR: Python 3.12.8 installation failed or PATH was not updated. >> "Secure_Distribution\install.bat"
echo         echo Please restart your computer and run this script again. >> "Secure_Distribution\install.bat"
echo         echo If the problem persists, install Python 3.12.8 manually. >> "Secure_Distribution\install.bat"
echo         pause >> "Secure_Distribution\install.bat"
echo         exit /b 1 >> "Secure_Distribution\install.bat"
echo     ) >> "Secure_Distribution\install.bat"
echo ) >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Python 3.12 is installed and ready to use. >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo :: Create and activate virtual environment >> "Secure_Distribution\install.bat"
echo echo Creating virtual environment... >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo if exist "venv" rmdir /s /q "venv" >> "Secure_Distribution\install.bat"
echo python -m venv venv >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Activating virtual environment... >> "Secure_Distribution\install.bat"
echo call venv\Scripts\activate.bat >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Upgrading pip... >> "Secure_Distribution\install.bat"
echo python -m pip install --upgrade pip >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Installing required dependencies... >> "Secure_Distribution\install.bat"
echo pip install -r requirements.txt >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Installing additional dependencies... >> "Secure_Distribution\install.bat"
echo pip install together >> "Secure_Distribution\install.bat"
echo pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1 imageio-ffmpeg==0.5.1 proglog==0.1.10 >> "Secure_Distribution\install.bat"
echo echo Installing Modern UI dependencies... >> "Secure_Distribution\install.bat"
echo pip install ttkthemes>=3.2.2 >> "Secure_Distribution\install.bat"
echo pip install Pillow>=10.2.0 >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Creating default configuration... >> "Secure_Distribution\install.bat"
echo echo { > "config.json" >> "Secure_Distribution\install.bat"
echo echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "config.json" >> "Secure_Distribution\install.bat"
echo echo   "whatsapp_number": "+8801734690909", >> "config.json" >> "Secure_Distribution\install.bat"
echo echo   "tts": { >> "config.json" >> "Secure_Distribution\install.bat"
echo echo     "speech_rate": 1.0 >> "config.json" >> "Secure_Distribution\install.bat"
echo echo   } >> "config.json" >> "Secure_Distribution\install.bat"
echo echo } >> "config.json" >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo Installation complete! >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo echo Setting up Python path for modules... >> "Secure_Distribution\install.bat"
echo echo # Make sure src directory is in Python path > "Secure_Distribution\src\__init__.py" >> "Secure_Distribution\install.bat"
echo echo # Make services a proper package > "Secure_Distribution\src\services\__init__.py" >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo echo You can now run the application using run.bat >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo pause >> "Secure_Distribution\install.bat"

:: Create README file
echo Creating README.txt...
echo # Modern 1ClickVideo v6.5 > "Secure_Distribution\README.txt"
echo ## Contemporary UI with Modern Design Principles >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Features >> "Secure_Distribution\README.txt"
echo - Modern Material Design inspired interface >> "Secure_Distribution\README.txt"
echo - Contemporary color palette and typography >> "Secure_Distribution\README.txt"
echo - Sleek buttons with hover effects and smooth transitions >> "Secure_Distribution\README.txt"
echo - Organized tab navigation for better user experience >> "Secure_Distribution\README.txt"
echo - Real-time status monitoring and professional console >> "Secure_Distribution\README.txt"
echo - Complete feature parity with enhanced usability >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Installation >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo 1. Double-click install.bat to set up the application >> "Secure_Distribution\README.txt"
echo    - This will check if Python 3.12.8 is installed >> "Secure_Distribution\README.txt"
echo    - If Python is not installed, it will download and install it >> "Secure_Distribution\README.txt"
echo    - It will create a virtual environment and install all required dependencies >> "Secure_Distribution\README.txt"
echo    - Modern UI dependencies (ttkthemes, Pillow) will be automatically installed >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo 2. After installation, run the application by double-clicking run.bat >> "Secure_Distribution\README.txt"
echo    - Alternatively, use run_modern_ui.bat for enhanced Modern UI experience >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Modern UI Features >> "Secure_Distribution\README.txt"
echo - Material Design inspired interface with contemporary aesthetics >> "Secure_Distribution\README.txt"
echo - ModernColors class with carefully selected color palette >> "Secure_Distribution\README.txt"
echo - ModernButton components with hover effects and smooth transitions >> "Secure_Distribution\README.txt"
echo - ModernCard containers with subtle shadows and rounded appearance >> "Secure_Distribution\README.txt"
echo - Organized tab navigation for better workflow >> "Secure_Distribution\README.txt"
echo - Real-time status monitoring with professional console >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Testing >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo To verify the Modern UI build is working correctly: >> "Secure_Distribution\README.txt"
echo python test_modern_ui_build.py >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Login >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo You will need a valid login to use the application. If you don't have access yet, >> "Secure_Distribution\README.txt"
echo you can purchase a license by clicking the "Purchase License" button. >> "Secure_Distribution\README.txt"

:: Verify build integrity
echo.
echo Verifying build integrity...
set "build_errors=0"

:: Check if critical files exist
if not exist "Secure_Distribution\launch_modern_ui.py" (
    echo ERROR: launch_modern_ui.py not found in distribution
    set /a build_errors+=1
)

if not exist "Secure_Distribution\src\modern_ui.py" (
    echo ERROR: modern_ui.py not found in distribution
    set /a build_errors+=1
)

if not exist "Secure_Distribution\run.bat" (
    echo ERROR: run.bat not found in distribution
    set /a build_errors+=1
)

if not exist "Secure_Distribution\install.bat" (
    echo ERROR: install.bat not found in distribution
    set /a build_errors+=1
)

:: Count Python files in source vs distribution
for /f %%i in ('dir /s /b "src\*.py" 2^>nul ^| find /c ".py"') do set "source_files=%%i"
for /f %%i in ('dir /s /b "Secure_Distribution\src\*.py" 2^>nul ^| find /c ".py"') do set "dist_files=%%i"

echo Source Python files: %source_files%
echo Distribution Python files: %dist_files%

if %source_files% NEQ %dist_files% (
    echo WARNING: File count mismatch between source and distribution
    set /a build_errors+=1
)

:: Clean up temporary files
echo Cleaning up temporary files...
:: Note: Keeping obfuscator.py for future builds

echo.
if %build_errors% EQU 0 (
    echo =====================================================
    echo   BUILD COMPLETED SUCCESSFULLY!
    echo =====================================================
    echo.
    echo Modern 1ClickVideo v6.5 Secure Distribution Ready
    echo Location: Secure_Distribution folder
    echo.
    echo Security Features:
    echo - All Python files have been obfuscated
    echo - Source code is compressed and encoded
    echo - Enhanced protection against reverse engineering
    echo.
    echo Modern UI Features Included:
    echo - Contemporary Material Design interface
    echo - ModernColors, ModernButton, and ModernCard components
    echo - Enhanced user experience with modern design principles
    echo - Complete feature parity with sleek new interface
    echo.
    echo To install and run:
    echo 1. Navigate to Secure_Distribution folder
    echo 2. Run install.bat to set up environment
    echo 3. Run run.bat to start the application
    echo.
) else (
    echo =====================================================
    echo   BUILD COMPLETED WITH %build_errors% ERROR(S)
    echo =====================================================
    echo.
    echo Please review the errors above and rebuild if necessary.
    echo The distribution may still be functional, but some features might be missing.
    echo.
)

echo Press any key to exit...
pause > nul