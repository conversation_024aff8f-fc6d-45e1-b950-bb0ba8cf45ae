import os
import tempfile
import subprocess
import json
from typing import List, Dict, Any, Optional, <PERSON><PERSON>, Union
from moviepy.editor import (
    VideoFileClip,
    TextClip,
    CompositeVideoClip,
    ImageClip,
    ColorClip
)
from openai import OpenAI
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import ai_client
import re

# Define animation styles
ANIMATION_STYLES = {
    "none": "No animation"
}

def get_transcription(video_file: str, api_key: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get transcription with word-level timestamps using the selected AI provider (OpenAI or Groq)"""
    # Extract audio from video file
    temp_audio_file = tempfile.mktemp(suffix=".mp3")

    try:
        # Use ffmpeg to extract audio
        subprocess.call([
            "ffmpeg", "-y", "-i", video_file,
            "-vn", "-acodec", "libmp3lame", "-q:a", "2",
            temp_audio_file
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

        # Check which AI provider is currently selected
        current_provider = ai_client.current_provider
        print(f"Getting transcription using {current_provider.upper()} API...")

        # Use our AI client manager to handle transcription with the current provider
        try:
            # Request verbose_json format with word-level timestamps
            print(f"Requesting transcription from {current_provider} with word-level timestamps...")
            transcription = ai_client.transcribe_audio(
                audio_file=temp_audio_file,
                response_format="verbose_json"
            )

            # Check if transcription has words attribute (OpenAI format)
            if hasattr(transcription, 'words') and transcription.words:
                print(f"Successfully received word-level transcription from {current_provider} (OpenAI format)")
                return transcription.words
            # Check for Groq format (dictionary with 'words' key)
            elif isinstance(transcription, dict) and 'words' in transcription:
                print(f"Successfully received word-level transcription from {current_provider} (Groq format)")
                words_list = transcription['words']

                # Add additional validation and fixing for Groq format
                validated_words = []
                video_duration = None

                try:
                    # Get video duration for fallback timing calculations
                    video_clip = VideoFileClip(video_file)
                    video_duration = video_clip.duration
                    video_clip.close()
                    print(f"Video duration: {video_duration} seconds")
                except Exception as e:
                    print(f"Could not get video duration: {str(e)}")

                # Process and validate each word
                for i, word in enumerate(words_list):
                    # Ensure each word has necessary attributes
                    if not isinstance(word, dict):
                        word = {"word": str(word), "text": str(word)}

                    # Ensure word has text/word attribute
                    if "text" not in word and "word" not in word:
                        continue

                    # Make sure we have the word text in both formats for compatibility
                    if "text" not in word and "word" in word:
                        word["text"] = word["word"]
                    if "word" not in word and "text" in word:
                        word["word"] = word["text"]

                    # Validate start and end times
                    if "start" not in word or "end" not in word or not isinstance(word["start"], (int, float)) or not isinstance(word["end"], (int, float)):
                        # Calculate estimated timing if video duration is known
                        if video_duration:
                            # Distribute words evenly across video duration
                            total_words = len(words_list)
                            word["start"] = (i / total_words) * video_duration
                            word["end"] = ((i + 1) / total_words) * video_duration
                        else:
                            # Use index-based timing (arbitrary 5 second span)
                            word["start"] = i * 0.5
                            word["end"] = (i + 1) * 0.5

                    # Ensure start time is less than end time
                    if word["start"] >= word["end"]:
                        word["end"] = word["start"] + 0.5

                    validated_words.append(word)

                print(f"Validated {len(validated_words)} words from Groq transcription")
                return validated_words

            # Check for segments format (some Groq responses)
            elif isinstance(transcription, dict) and 'segments' in transcription:
                print(f"Received segments from {current_provider}, extracting words")
                words = []

                # Get video duration for fallback timing if needed
                video_duration = None
                try:
                    video_clip = VideoFileClip(video_file)
                    video_duration = video_clip.duration
                    video_clip.close()
                    print(f"Video duration: {video_duration} seconds")
                except Exception as e:
                    print(f"Could not get video duration: {str(e)}")

                # Extract and process words from segments
                total_segments = len(transcription['segments'])
                for i, segment in enumerate(transcription['segments']):
                    segment_start = segment.get('start', (i / total_segments) * (video_duration or 10.0))
                    segment_end = segment.get('end', ((i + 1) / total_segments) * (video_duration or 10.0))
                    segment_text = segment.get('text', '')

                    if 'words' in segment and segment['words']:
                        # Use provided words
                        for word in segment['words']:
                            if not isinstance(word, dict):
                                word = {"word": str(word), "text": str(word)}

                            # Ensure word has required fields
                            if "text" not in word and "word" not in word:
                                continue

                            # Make sure we have the word text in both formats for compatibility
                            if "text" not in word and "word" in word:
                                word["text"] = word["word"]
                            if "word" not in word and "text" in word:
                                word["word"] = word["text"]

                            # Validate timing, use segment timing as fallback
                            if "start" not in word or not isinstance(word["start"], (int, float)):
                                word["start"] = segment_start
                            if "end" not in word or not isinstance(word["end"], (int, float)):
                                word["end"] = segment_end

                            # Ensure start time is less than end time
                            if word["start"] >= word["end"]:
                                word["end"] = word["start"] + 0.5

                            words.append(word)
                    else:
                        # Create words from segment text
                        word_texts = segment_text.split()
                        word_duration = (segment_end - segment_start) / len(word_texts) if word_texts else 0.5

                        for j, word_text in enumerate(word_texts):
                            word_start = segment_start + j * word_duration
                            word_end = word_start + word_duration
                            words.append({
                                "word": word_text,
                                "text": word_text,
                                "start": word_start,
                                "end": word_end
                            })

                if words:
                    print(f"Extracted {len(words)} words from segments")
                    return words

            # If we get here, we couldn't find word-level data in the expected formats
            print(f"Warning: Received transcription from {current_provider} but no word-level data found")
            print(f"Transcription type: {type(transcription)}")

            # Try to extract text content for fallback
            text = None
            if hasattr(transcription, 'text'):
                text = transcription.text
            elif isinstance(transcription, dict) and 'text' in transcription:
                text = transcription['text']

            if text:
                print(f"Creating basic word-level data from text: {text[:50]}...")
                # Create a simple word structure with estimated timings
                words = []
                text_words = text.split()
                total_duration = 5.0  # Assume 5 seconds if we don't know
                time_per_word = total_duration / len(text_words) if text_words else 0.5

                for i, word in enumerate(text_words):
                    words.append({
                        "word": word,
                        "start": i * time_per_word,
                        "end": (i + 1) * time_per_word,
                        "text": word
                    })
                return words

            # If we get here and haven't returned, we couldn't create word data
            raise Exception("No word-level data available in transcription")

        except Exception as e:
            print(f"Error using {current_provider} for transcription: {str(e)}")
            print("Falling back to OpenAI Whisper API...")

            try:
                # If using AI client fails, fall back to direct OpenAI API call
                client = OpenAI(api_key=api_key)
                with open(temp_audio_file, "rb") as audio_file:
                    transcription = client.audio.transcriptions.create(
                        file=audio_file,
                        model="whisper-1",
                        response_format="verbose_json",
                        timestamp_granularities=["word"]
                    )
                print("Successfully received word-level transcription from OpenAI fallback")
                return transcription.words
            except Exception as fallback_error:
                print(f"Error with OpenAI fallback: {str(fallback_error)}")
                # Create a minimal set of words as a last resort
                print("Creating minimal word data as last resort")
                return [{
                    "word": "[No transcription available]",
                    "start": 0.0,
                    "end": 2.0,
                    "text": "[No transcription available]"
                }]

    finally:
        # Clean up temporary audio file
        if os.path.exists(temp_audio_file):
            os.remove(temp_audio_file)

def create_text_image(
    text: str,
    font_path: str,
    font_size: int,
    font_color: str,
    stroke_width: int,
    stroke_color: str,
    image_width: int,
    image_height: int,
    highlight_words: Optional[List[Tuple[int, int, str]]] = None,
    highlight_style: str = "text_color",
    highlight_bg_color: str = "#3700B3",
    highlight_bg_opacity: float = 0.7
) -> Image.Image:
    """
    Create an image with text, with optional word highlighting.

    Args:
        text: Text to render
        font_path: Path to TTF font file
        font_size: Font size in pixels
        font_color: Font color (name or hex code)
        stroke_width: Width of text outline
        stroke_color: Color of text outline
        image_width: Width of the output image
        image_height: Height of the output image
        highlight_words: Optional list of (start_index, end_index, color) tuples for highlighting specific words
        highlight_style: Style of highlighting ("text_color" or "background")
        highlight_bg_color: Background color for highlighted words when using "background" style
        highlight_bg_opacity: Opacity of the background highlight (0.0 to 1.0)

    Returns:
        PIL Image with rendered text
    """
    # Create a transparent image
    text_img = Image.new("RGBA", (image_width, image_height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(text_img)

    # Calculate horizontal padding (15% of image width on each side)
    h_padding = int(image_width * 0.15)
    max_text_width = image_width - (h_padding * 2)

    # Try to load the font
    try:
        # Auto-adjust font size based on word count and text length
        word_count = len(text.split())
        text_length = len(text)

        # Scale font down for more words or very long text
        adjusted_font_size = font_size
        if word_count > 4:
            # Reduce font size by 10% for each word beyond 4
            reduction_factor = max(0.6, 1.0 - ((word_count - 4) * 0.1))
            adjusted_font_size = int(font_size * reduction_factor)

        # Further adjust for very long text
        if text_length > 100:
            # Reduce by additional amount based on length
            length_factor = max(0.7, 1.0 - ((text_length - 100) / 400))
            adjusted_font_size = int(adjusted_font_size * length_factor)

        # Ensure font size doesn't go below minimum readable size
        adjusted_font_size = max(24, adjusted_font_size)

        print(f"Original font size: {font_size}, Adjusted for {word_count} words: {adjusted_font_size}")

        # Check if font_path is a full path to a font file
        if os.path.exists(font_path) and (font_path.lower().endswith('.ttf') or font_path.lower().endswith('.otf')):
            # It's a full path to a font file, use it directly
            print(f"Loading font from path: {font_path}")
            try:
                font = ImageFont.truetype(font_path, adjusted_font_size)
                print(f"Successfully loaded font from path: {font_path}")
            except Exception as e:
                print(f"Error loading font from path {font_path}: {str(e)}")
                # Continue to fallback options
                font = None
        else:
            # Try multiple approaches to find the font
            font = None

        # If font is still None, try other approaches
        if font is None:
            # Try to find the font in the font directory
            font_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "font")
            font_found = False

            # First, try with exact name (no extension)
            for ext in [".ttf", ".otf"]:
                potential_path = os.path.join(font_dir, f"{font_path}{ext}")
                if os.path.exists(potential_path):
                    print(f"Loading font from: {potential_path}")
                    try:
                        font = ImageFont.truetype(potential_path, adjusted_font_size)
                        font_found = True
                        print(f"Successfully loaded font from: {potential_path}")
                        break
                    except Exception as e:
                        print(f"Error loading font from {potential_path}: {str(e)}")
                        # Continue to next attempt

            # If not found, try removing "-Regular" suffix if present
            if not font_found and "-Regular" in font_path:
                base_name = font_path.replace("-Regular", "")
                for ext in [".ttf", ".otf"]:
                    potential_path = os.path.join(font_dir, f"{base_name}{ext}")
                    if os.path.exists(potential_path):
                        print(f"Loading font from: {potential_path} (removed -Regular suffix)")
                        try:
                            font = ImageFont.truetype(potential_path, adjusted_font_size)
                            font_found = True
                            print(f"Successfully loaded font from: {potential_path}")
                            break
                        except Exception as e:
                            print(f"Error loading font from {potential_path}: {str(e)}")
                            # Continue to next attempt

            # If still not found, try as a system font name
            if not font_found:
                try:
                    print(f"Trying to load system font: {font_path}")
                    font = ImageFont.truetype(font_path, adjusted_font_size)
                    font_found = True
                    print(f"Successfully loaded system font: {font_path}")
                except OSError:
                    # Try with Arial
                    try:
                        print(f"Font {font_path} not found, trying Arial")
                        font = ImageFont.truetype("Arial", adjusted_font_size)
                        font_found = True
                        print("Successfully loaded Arial font")
                    except OSError:
                        print("Arial not found, using default font")
                        font = ImageFont.load_default()
    except Exception as e:
        # Fallback to default font with detailed error
        print(f"Error loading font {font_path}: {str(e)}")
        try:
            print("Trying Arial as fallback")
            font = ImageFont.truetype("Arial", adjusted_font_size)
        except:
            print("Using default font as last resort")
            font = ImageFont.load_default()

    # If no highlighting is needed, render the regular text with stroke
    if not highlight_words:
        # Add stroke (outline) to text
        if stroke_width > 0:
            # Draw text in multiple positions offset by the stroke width
            for offset_x in range(-stroke_width, stroke_width+1, 2):
                for offset_y in range(-stroke_width, stroke_width+1, 2):
                    if offset_x == 0 and offset_y == 0:
                        continue
                    draw.text(
                        (image_width // 2 + offset_x, image_height // 2 + offset_y),
                        text,
                        font=font,
                        fill=stroke_color,
                        anchor="mm",
                        align="center"
                    )

        # Draw the main text centered
        draw.text(
            (image_width // 2, image_height // 2),
            text,
            font=font,
            fill=font_color,
            anchor="mm",
            align="center"
        )
    else:
        # For word highlighting, we need to calculate text positioning more carefully
        # Get total text size
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]

        # Center position
        x_center = image_width // 2
        y_center = image_height // 2

        # Starting positions for left-aligned text that will be centered
        x_start = x_center - (text_width // 2)
        y_start = y_center - (text_height // 2)

        # First, draw the entire text with stroke for the outline effect
        if stroke_width > 0:
            for offset_x in range(-stroke_width, stroke_width+1, 2):
                for offset_y in range(-stroke_width, stroke_width+1, 2):
                    if offset_x == 0 and offset_y == 0:
                        continue
                    draw.text(
                        (x_start + offset_x, y_start + offset_y),
                        text,
                        font=font,
                        fill=stroke_color
                    )

        # Split text into words to handle highlighting
        words = text.split()
        char_positions = []
        current_pos = 0

        # Calculate position of each word in the text
        for word in words:
            word_len = len(word)
            char_positions.append((current_pos, current_pos + word_len))
            current_pos += word_len + 1  # +1 for the space

        # Now draw each part of the text with appropriate colors
        current_pos = 0

        # We'll draw the text in parts to avoid duplicate rendering
        # Don't draw the entire text first - we'll draw each part individually

        # We need to draw all text parts - both highlighted and non-highlighted
        # First, identify all segments that need to be drawn
        segments = []
        last_end = 0

        # Sort highlight_words by start_idx to ensure we process them in order
        sorted_highlights = sorted(highlight_words, key=lambda x: x[0])

        # Create segments for all parts of the text
        for start_idx, end_idx, highlight_color in sorted_highlights:
            if 0 <= start_idx < len(text) and start_idx < end_idx <= len(text):
                # If there's text before this highlight, add it as a non-highlighted segment
                if start_idx > last_end:
                    segments.append((last_end, start_idx, None))  # None means no highlight

                # Add the highlighted segment
                segments.append((start_idx, end_idx, highlight_color))
                last_end = end_idx

        # Add any remaining text after the last highlight
        if last_end < len(text):
            segments.append((last_end, len(text), None))

        # Now draw each segment
        for start_idx, end_idx, highlight_color in segments:
            segment_text = text[start_idx:end_idx]

            # Calculate position of this segment
            segment_before = text[:start_idx]
            before_bbox = draw.textbbox((0, 0), segment_before, font=font)
            before_width = before_bbox[2] - before_bbox[0]

            # Get the bounding box of the segment text
            segment_bbox = draw.textbbox((0, 0), segment_text, font=font)
            segment_width = segment_bbox[2] - segment_bbox[0]
            segment_height = segment_bbox[3] - segment_bbox[1]

            # If this is a non-highlighted segment, just draw it with the normal color
            if highlight_color is None:
                draw.text(
                    (x_start + before_width, y_start),
                    segment_text,
                    font=font,
                    fill=font_color
                )
                continue

            # For highlighted segments, continue with the existing highlight logic
            highlighted_text = segment_text
            highlighted_width = segment_width
            highlighted_height = segment_height

            if highlight_style == "background":
                    # Convert hex color to RGBA with opacity
                    bg_color = highlight_bg_color
                    if bg_color.startswith('#'):
                        r = int(bg_color[1:3], 16)
                        g = int(bg_color[3:5], 16)
                        b = int(bg_color[5:7], 16)
                        a = int(255 * highlight_bg_opacity)
                        bg_color_rgba = (r, g, b, a)
                    else:
                        # For named colors, use a semi-transparent version
                        bg_color_rgba = bg_color + (int(255 * highlight_bg_opacity),)

                    # Draw a rounded rectangle background
                    # Increase padding around the text for better coverage
                    padding_x = int(font_size * 0.15)  # Increased from 0.1 to 0.15
                    padding_y = int(font_size * 0.2)   # Increased from 0.05 to 0.2 for better vertical coverage

                    # Calculate rectangle coordinates with equal padding on all sides
                    # For horizontal positioning
                    rect_x1 = int(x_start + before_width - padding_x)  # Convert to int to avoid float issues
                    rect_x2 = int(rect_x1 + highlighted_width + (padding_x * 2))  # Convert to int

                    # For vertical positioning - use the exact same baseline for all text
                    # Get the text descent (how far below baseline text goes)
                    # This is critical for proper vertical alignment
                    text_descent = segment_bbox[3] - segment_bbox[1]

                    # Calculate rectangle coordinates to ensure text sits on same baseline
                    rect_y1 = int(y_start - padding_y)  # Top of background
                    rect_y2 = int(y_start + text_descent + padding_y)  # Bottom of background

                    # Draw the background rounded rectangle
                    # Calculate corner radius (20% of font size, but max 10px)
                    # Ensure corner_radius is an integer to avoid PIL errors
                    corner_radius = int(min(int(font_size * 0.2), 10))

                    try:
                        # Create a new image for the rounded rectangle background
                        # Ensure dimensions are integers and at least 1 pixel
                        bg_width = max(1, rect_x2 - rect_x1)
                        bg_height = max(1, rect_y2 - rect_y1)
                        bg_img = Image.new('RGBA', (bg_width, bg_height), (0, 0, 0, 0))
                        bg_draw = ImageDraw.Draw(bg_img)

                        # Ensure corner radius is not too large for the rectangle
                        # Corner radius should not be more than half the smallest dimension
                        max_radius = min(bg_width, bg_height) // 2
                        safe_corner_radius = min(corner_radius, max_radius)

                        # Draw the rounded rectangle on the background image
                        try:
                            bg_draw.rounded_rectangle(
                                [0, 0, bg_width - 1, bg_height - 1],  # Use -1 to stay within bounds
                                radius=safe_corner_radius,
                                fill=bg_color_rgba
                            )
                        except TypeError:
                            # Some versions of PIL require int for radius
                            bg_draw.rounded_rectangle(
                                [0, 0, bg_width - 1, bg_height - 1],
                                radius=int(safe_corner_radius),
                                fill=bg_color_rgba
                            )

                        # Paste the background image onto the main image
                        text_img.paste(bg_img, (rect_x1, rect_y1), bg_img)
                    except (AttributeError, TypeError, ValueError) as e:
                        # Fallback for older PIL versions that don't support rounded_rectangle
                        print(f"Warning: Rounded rectangle not supported, using regular rectangle: {e}")
                        # Ensure rectangle coordinates are valid
                        draw.rectangle(
                            [rect_x1, rect_y1, rect_x2, rect_y2],
                            fill=bg_color_rgba
                        )

                    # Draw the text over the background with the original color
                    # Calculate the horizontal center of the background rectangle
                    bg_center_x = int(rect_x1 + (rect_x2 - rect_x1) / 2)

                    # Draw the text with precise baseline alignment
                    # Use the "ma" anchor (middle-ascender) for consistent baseline alignment
                    # This ensures all text sits on the same baseline regardless of highlighting
                    try:
                        draw.text(
                            (bg_center_x, y_start),
                            highlighted_text,
                            font=font,
                            fill=font_color,
                            anchor="ma"  # Middle-ascender anchor for consistent baseline alignment
                        )
                    except (TypeError, ValueError):
                        # Fallback for older PIL versions that don't support the anchor parameter
                        # Calculate text position manually to align with baseline
                        text_bbox = draw.textbbox((0, 0), highlighted_text, font=font)
                        text_width = text_bbox[2] - text_bbox[0]
                        text_ascent = text_bbox[1]  # Distance from top to baseline

                        # Position text with the same baseline as non-highlighted text
                        draw.text(
                            (bg_center_x - (text_width / 2), y_start - text_ascent),
                            highlighted_text,
                            font=font,
                            fill=font_color
                        )
            else:
                # Default to text color highlighting
                draw.text(
                    (x_start + before_width, y_start),
                    highlighted_text,
                    font=font,
                    fill=highlight_color
                )

    return text_img

def add_captions(
    video_file: str,
    output_file: str,
    font: str = "Arial",
    font_size: int = 40,
    font_color: str = "white",
    stroke_width: int = 3,
    stroke_color: str = "black",
    highlight_current_word: bool = True,
    word_highlight_color: str = "yellow",
    words_per_caption: int = 3,
    line_count: int = 1,  # Keeping for backward compatibility
    padding: int = 70,
    position: str = "bottom",
    animation_style: str = "none",  # Kept for backward compatibility
    api_key: Optional[str] = None,
    highlight_style: str = "text_color",
    highlight_bg_color: str = "#3700B3",
    highlight_bg_opacity: float = 0.7
) -> None:
    """
    Add captions to a video file with custom font rendering and word highlighting.

    Args:
        video_file: Path to the input video file
        output_file: Path to the output video file
        font: Path to a TTF font file or a font name
        font_size: Font size in pixels
        font_color: Font color (name or hex code)
        stroke_width: Width of the text outline
        stroke_color: Color of the text outline
        highlight_current_word: Whether to highlight the current word
        word_highlight_color: Color for the highlighted word
        words_per_caption: Number of words to display at once in a caption
                           Use 0 to display full sentences
        line_count: (Deprecated) Maximum number of lines to display at once
        padding: Padding from the edge of the video in pixels
        position: Position of captions ("top", "center", "bottom")
        animation_style: (Deprecated) Kept for backward compatibility
        api_key: OpenAI API key (optional)
        highlight_style: Style of highlighting ("text_color" or "background")
        highlight_bg_color: Background color for highlighted words when using "background" style
        highlight_bg_opacity: Opacity of the background highlight (0.0 to 1.0)
    """
    # Get video clip
    print(f"Loading video: {video_file}")
    video = VideoFileClip(video_file)
    video_duration = video.duration

    # Get transcription
    words = get_transcription(video_file, api_key)

    if not words:
        print("No transcription found.")
        return

    # Additional validation for all words to ensure proper timing
    valid_words = []
    for i, word in enumerate(words):
        # Skip words without text content
        word_text = None
        if hasattr(word, 'text'):
            word_text = word.text
        elif hasattr(word, 'word'):
            word_text = word.word
        elif isinstance(word, dict):
            word_text = word.get('text', word.get('word', None))

        if not word_text or str(word_text).strip() == "":
            continue

        # Create a clean word object (dictionary format)
        clean_word = {}
        if isinstance(word, dict):
            clean_word = word.copy()
        else:
            # Convert object to dictionary
            if hasattr(word, '__dict__'):
                clean_word = word.__dict__.copy()
            else:
                # Create from scratch
                clean_word = {}
                if hasattr(word, 'text'):
                    clean_word['text'] = word.text
                if hasattr(word, 'word'):
                    clean_word['word'] = word.word
                if hasattr(word, 'start'):
                    clean_word['start'] = word.start
                if hasattr(word, 'end'):
                    clean_word['end'] = word.end

        # Ensure word has both text and word keys
        if 'text' not in clean_word and 'word' not in clean_word:
            # Skip words without text
            continue
        if 'text' not in clean_word and 'word' in clean_word:
            clean_word['text'] = clean_word['word']
        if 'word' not in clean_word and 'text' in clean_word:
            clean_word['word'] = clean_word['text']

        # Validate start and end times
        timing_is_valid = True

        # Check start time
        if 'start' not in clean_word or not isinstance(clean_word['start'], (int, float)):
            timing_is_valid = False
        elif clean_word['start'] < 0 or clean_word['start'] > video_duration:
            timing_is_valid = False

        # Check end time
        if 'end' not in clean_word or not isinstance(clean_word['end'], (int, float)):
            timing_is_valid = False
        elif clean_word['end'] < 0 or clean_word['end'] > video_duration:
            timing_is_valid = False

        # Check start < end
        if timing_is_valid and clean_word['start'] >= clean_word['end']:
            timing_is_valid = False

        # Fix timing if invalid
        if not timing_is_valid:
            # Assign estimated timing based on position
            total_words = len(words)
            clean_word['start'] = (i / total_words) * video_duration
            clean_word['end'] = ((i + 1) / total_words) * video_duration

        valid_words.append(clean_word)

    # Replace original words with validated ones if we have valid words
    if valid_words:
        print(f"Using {len(valid_words)} validated words out of {len(words)} original words")
        words = valid_words
    else:
        print("WARNING: No valid words found in transcription")
        return

    # Verify that we have valid word objects with timing information
    has_timing = False
    for word in words[:10]:  # Check first 10 words
        # Check if word has timing information
        if (hasattr(word, 'start') and hasattr(word, 'end')) or \
           (isinstance(word, dict) and ('start' in word and 'end' in word)):
            has_timing = True
            break

    if not has_timing:
        print("WARNING: Transcription doesn't have proper word-level timing information")
        print("Creating estimated timing information based on video duration")

        # Create estimated timing information
        word_count = len(words)
        time_per_word = video_duration / word_count if word_count else 0.5

        # Create new words list with timing information
        new_words = []
        for i, word in enumerate(words):
            # Extract text from word
            if hasattr(word, 'text'):
                text = word.text
            elif hasattr(word, 'word'):
                text = word.word
            elif isinstance(word, dict):
                text = word.get('text', word.get('word', str(word)))
            else:
                text = str(word)

            # Create new word object with timing
            new_words.append({
                'text': text,
                'word': text,
                'start': i * time_per_word,
                'end': (i + 1) * time_per_word
            })

        words = new_words
        print(f"Created {len(words)} words with estimated timing")

    print(f"Transcription complete. Found {len(words)} words.")

    if words_per_caption == 0:
        print("Using full sentence mode for captions")
    else:
        print(f"Using {words_per_caption} words per caption")

    # Calculate safe margin for text (15% from each side)
    horizontal_margin = int(video.w * 0.15)

    # Group words into captions based on words_per_caption or sentences
    word_groups = []
    current_group = []
    current_sentence = []
    last_word_ends_sentence = False

    # Add detailed logging
    print(f"Starting to process {len(words)} words into groups")
    print(f"First few words: {words[:3] if len(words) >= 3 else words}")

    # Ensure we have at least one valid word
    if not words:
        print("WARNING: No words found in transcription. Creating a placeholder word.")
        placeholder_word = {
            'text': "No transcription available",
            'word': "No transcription available",
            'start': 0.0,
            'end': video_duration
        }
        words = [placeholder_word]

    for word in words:
        # Skip empty words
        # Extract text from word object, handling different formats
        text = None

        # Try attribute access first (OpenAI format)
        if hasattr(word, 'text'):
            text = word.text
        elif hasattr(word, 'word'):
            text = word.word
        # Try dictionary access (Groq format)
        elif isinstance(word, dict):
            if 'text' in word:
                text = word['text']
            elif 'word' in word:
                text = word['word']

        # Last resort - convert to string
        if text is None:
            try:
                text = str(word)
            except:
                text = "[Unknown]"  # Absolute last resort

        if not text or text.strip() == "":
            # Instead of skipping, use a placeholder
            text = "[Caption]"
            if hasattr(word, 'text'):
                word.text = text
            elif hasattr(word, 'word'):
                word.word = text
            elif isinstance(word, dict):
                word['text'] = text
                word['word'] = text

        # Check if this word ends a sentence (for full sentence mode)
        ends_sentence = text.rstrip().endswith(('.', '!', '?')) or text.rstrip().endswith(('."', '!"', '?"'))

        if words_per_caption == 0:
            # Full sentence mode
            current_sentence.append(word)

            if ends_sentence:
                # End of sentence, add to groups
                word_groups.append(current_sentence)
                print(f"Added sentence group with {len(current_sentence)} words: '{' '.join([w.get('text', w.get('word', str(w))) if isinstance(w, dict) else getattr(w, 'text', getattr(w, 'word', str(w))) for w in current_sentence][:3])}...'")
                current_sentence = []
        else:
            # Word count mode - strictly adhere to words_per_caption
            current_group.append(word)

            # Create a new group when we reach exactly the desired number of words
            # This ensures each caption has exactly the number of words specified by the user
            if len(current_group) == words_per_caption:
                word_groups.append(current_group)
                group_text = ' '.join([w.get('text', w.get('word', str(w))) if isinstance(w, dict) else getattr(w, 'text', getattr(w, 'word', str(w))) for w in current_group])
                print(f"Created caption group with exactly {len(current_group)} words: '{group_text}'")
                current_group = []

    # Add any remaining words
    if words_per_caption == 0 and current_sentence:
        word_groups.append(current_sentence)
        print(f"Added final sentence group with {len(current_sentence)} words")
    elif words_per_caption > 0 and current_group:
        word_groups.append(current_group)
        print(f"Added final word group with {len(current_group)} words")

    # Ensure we have at least one word group
    if not word_groups:
        print("WARNING: No word groups created. Creating a placeholder group.")
        placeholder_word = {
            'text': "No captions available",
            'word': "No captions available",
            'start': 0.0,
            'end': video_duration
        }
        word_groups.append([placeholder_word])

    # Log the total number of word groups created
    print(f"Created {len(word_groups)} word groups from {len(words)} words")
    if len(word_groups) > 0:
        print(f"First group has {len(word_groups[0])} words")
        if len(word_groups) > 1:
            print(f"Last group has {len(word_groups[-1])} words")

    # Create a list of subtitle clips
    subtitle_clips = []

    # Prepare font parameters
    print(f"Using font: {font}, size: {font_size}, color: {font_color}")
    print(f"Stroke width: {stroke_width}, color: {stroke_color}")
    print(f"Caption position: {position}")
    print(f"Animation style: {animation_style}")

    # Ensure image width matches video width for proper centering
    image_width = video.w

    # Collect all group timings first for adjustment
    group_timings = []

    # Calculate video duration for reference
    video_duration = video.duration

    # Process each word group to extract timing information
    for group in word_groups:
        if not group:
            continue

        # Get the text of all words in the group
        group_text = " ".join([
            word.get('text', word.get('word', str(word))) if isinstance(word, dict) else
            getattr(word, 'text', getattr(word, 'word', str(word)))
            for word in group
        ])

        # Validate group text - if it's empty or just contains whitespace, use a placeholder
        if not group_text or group_text.strip() == "":
            print(f"Empty text group at position {len(group_timings)}, using placeholder")
            group_text = "[Caption]"

        # Strip extra whitespace and clean up text
        group_text = group_text.strip()

        # Normalize multiple spaces
        group_text = re.sub(r'\s+', ' ', group_text)

        # Get timing from the first and last word in the group
        first_word = group[0]
        last_word = group[-1]

        # Get start time from first word
        start_time = 0  # Default if we can't extract

        # Try attribute access first (OpenAI format)
        if hasattr(first_word, 'start'):
            start_time = first_word.start
        # Try dictionary access (Groq format)
        elif isinstance(first_word, dict) and 'start' in first_word:
            start_time = first_word['start']
        # Try dictionary access with different key names
        elif isinstance(first_word, dict) and 'start_time' in first_word:
            start_time = first_word['start_time']

        # Get end time from last word
        # Default to start_time + estimated duration if we can't extract
        end_time = start_time + 0.5 * len(group)

        # Try attribute access first (OpenAI format)
        if hasattr(last_word, 'end'):
            end_time = last_word.end
        # Try dictionary access (Groq format)
        elif isinstance(last_word, dict) and 'end' in last_word:
            end_time = last_word['end']
        # Try dictionary access with different key names
        elif isinstance(last_word, dict) and 'end_time' in last_word:
            end_time = last_word['end_time']

        # Calculate duration
        duration = end_time - start_time

        # Accept all durations, even very short ones
        # This ensures all captions are displayed, similar to captacity

        # Store the timing information along with the group
        group_timings.append((group, group_text, start_time, end_time))

    # Sort group timings by start time
    group_timings.sort(key=lambda x: x[2])

    # Minimum duration for captions with invalid timing
    MIN_CAPTION_DURATION = 0.5  # Minimum duration for captions with invalid timing

    # Print timing information for debugging
    print(f"Total captions: {len(group_timings)}")
    if len(group_timings) > 0:
        first_start = group_timings[0][2]
        last_end = group_timings[-1][3]
        print(f"Caption timing range: {first_start:.2f}s to {last_end:.2f}s (total: {last_end - first_start:.2f}s)")
        print(f"Video duration: {video.duration:.2f}s")

    # Adjust timing to prevent overlapping subtitles
    print("Adjusting caption timing to prevent overlaps")

    # Calculate total available time
    video_duration = video.duration

    # First pass: Ensure captions don't extend beyond video duration
    for i in range(len(group_timings)):
        group, group_text, start_time, end_time = group_timings[i]

        # If caption extends beyond video duration, adjust it
        if end_time > video_duration:
            new_end = video_duration
            new_start = min(start_time, new_end - MIN_CAPTION_DURATION)
            print(f"Adjusting caption that extends beyond video: {start_time:.2f}-{end_time:.2f} → {new_start:.2f}-{new_end:.2f}")
            group_timings[i] = (group, group_text, new_start, new_end)

    # Second pass: Adjust timings to prevent overlapping subtitles
    # Add a small buffer between captions (50ms)
    CAPTION_BUFFER = 0.05

    # Adjust end times to prevent overlaps
    for i in range(len(group_timings) - 1):
        current_group, current_text, current_start, current_end = group_timings[i]
        next_group, next_text, next_start, next_end = group_timings[i + 1]

        # If current caption ends after next one starts, adjust the end time
        if current_end > next_start:
            # Set current end time to be slightly before next start time
            new_end = max(current_start + 0.1, next_start - CAPTION_BUFFER)
            print(f"Adjusting overlapping caption: '{current_text}' end time {current_end:.2f}s → {new_end:.2f}s")
            group_timings[i] = (current_group, current_text, current_start, new_end)

    # Now process each group with adjusted timings
    for group, group_text, start_time, end_time in group_timings:

        # Calculate duration
        duration = end_time - start_time

        # Only skip captions with zero or negative duration
        if duration <= 0:
            print(f"Skipping caption '{group_text}' due to invalid duration: {duration:.2f}s")
            continue

        # Accept all durations, even very short ones
        # This ensures all captions are displayed with original timing

        # Create individual word timestamps for highlighting with improved extraction and validation
        word_times = []
        for i, word in enumerate(group):
            # Extract word text first to ensure we have valid content
            word_text = None
            if hasattr(word, 'text'):
                word_text = word.text
            elif hasattr(word, 'word'):
                word_text = word.word
            elif isinstance(word, dict):
                word_text = word.get('text', word.get('word', None))

            # If we couldn't extract text, use a placeholder
            if not word_text or not str(word_text).strip():
                word_text = f"[Word{i+1}]"

            # Get start time with multiple fallbacks
            word_start = None

            # First word always starts at the caption start time
            if i == 0:
                word_start = start_time
            else:
                # Try to get end time of previous word as start time for this word
                prev_word = group[i-1]

                # Try attribute access first (OpenAI format)
                if hasattr(prev_word, 'end'):
                    word_start = prev_word.end
                # Try dictionary access (Groq format)
                elif isinstance(prev_word, dict) and 'end' in prev_word:
                    word_start = prev_word['end']
                # Try dictionary access with different key names
                elif isinstance(prev_word, dict) and 'end_time' in prev_word:
                    word_start = prev_word['end_time']

            # If we still don't have a valid start time, estimate based on position
            if word_start is None or not isinstance(word_start, (int, float)) or word_start < start_time:
                word_start = start_time + (i / len(group)) * duration

            # Get end time with multiple fallbacks
            word_end = None

            # Try attribute access first (OpenAI format)
            if hasattr(word, 'end'):
                word_end = word.end
            # Try dictionary access (Groq format)
            elif isinstance(word, dict) and 'end' in word:
                word_end = word['end']
            # Try dictionary access with different key names
            elif isinstance(word, dict) and 'end_time' in word:
                word_end = word['end_time']

            # If we still don't have a valid end time, estimate based on position
            if word_end is None or not isinstance(word_end, (int, float)) or word_end <= word_start:
                word_end = start_time + ((i + 1) / len(group)) * duration

            # Ensure word timing is within caption timing
            # Use a smaller buffer (0.05s) to ensure smoother transitions
            word_start = max(start_time, min(word_start, end_time - 0.05))
            word_end = max(word_start + 0.05, min(word_end, end_time))

            # Add to word times
            word_times.append((word_text, word_start, word_end))

        try:
            font_loaded = False
            text_img = None

            # First try with the specified font
            try:
                # Create the base text image without highlighted words
                text_img = create_text_image(
                    text=group_text,
                    font_path=font,
                    font_size=font_size,
                    font_color=font_color,
                    stroke_width=stroke_width,
                    stroke_color=stroke_color,
                    image_width=image_width,
                    image_height=font_size * 3  # Height proportional to font size
                )
                font_loaded = True
                print(f"Font loaded successfully: {font}")
            except Exception as e:
                print(f"Error creating text image with primary font: {str(e)}")

                # Try with a full path to the font in the font directory
                try:
                    font_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "font", f"{font}")
                    if not os.path.exists(font_file):
                        # Try with .ttf extension
                        font_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "font", f"{font}.ttf")
                    if not os.path.exists(font_file):
                        # Try with .otf extension
                        font_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "font", f"{font}.otf")

                    if os.path.exists(font_file):
                        print(f"Trying with full font path: {font_file}")
                        text_img = create_text_image(
                            text=group_text,
                            font_path=font_file,
                            font_size=font_size,
                            font_color=font_color,
                            stroke_width=stroke_width,
                            stroke_color=stroke_color,
                            image_width=image_width,
                            image_height=font_size * 3
                        )
                        font_loaded = True
                        print(f"Font loaded successfully with full path: {font_file}")
                    else:
                        print(f"Font file not found: {font_file}")
                except Exception as e2:
                    print(f"Error with full font path: {str(e2)}")

            # If still not loaded, try with Arial
            if not font_loaded:
                try:
                    print("Using Arial as fallback font")
                    text_img = create_text_image(
                        text=group_text,
                        font_path="Arial",
                        font_size=font_size,
                        font_color=font_color,
                        stroke_width=stroke_width,
                        stroke_color=stroke_color,
                        image_width=image_width,
                        image_height=font_size * 3
                    )
                    font_loaded = True
                except Exception as e3:
                    print(f"Failed with Arial font: {str(e3)}")

            # Last resort - try with default font and reduced stroke
            if not font_loaded:
                try:
                    print("Using default font with reduced stroke as last resort")
                    # Create a simple text image with default font and minimal styling
                    text_img = Image.new("RGBA", (image_width, font_size * 3), (0, 0, 0, 0))
                    draw = ImageDraw.Draw(text_img)
                    default_font = ImageFont.load_default()

                    # Draw text with minimal styling
                    draw.text(
                        (image_width // 2, font_size * 3 // 2),
                        group_text,
                        font=default_font,
                        fill=font_color,
                        anchor="mm",
                        align="center"
                    )
                    font_loaded = True
                except Exception as e4:
                    print(f"Failed with default font: {str(e4)}")

        except Exception as e:
            print(f"Unexpected error in text rendering: {str(e)}")

        if text_img is None:
            print(f"Skipping word group '{group_text}' due to rendering issues")
            # Create a minimal text image as absolute last resort
            try:
                text_img = Image.new("RGBA", (image_width, font_size * 3), (0, 0, 0, 0))
                draw = ImageDraw.Draw(text_img)
                draw.text(
                    (image_width // 2, font_size * 3 // 2),
                    group_text[:50] + "..." if len(group_text) > 50 else group_text,
                    fill="white",
                    anchor="mm"
                )
                print("Created minimal fallback text image")
            except:
                print("Failed to create even a minimal text image, skipping this caption")
                continue

        # If highlighting is enabled, create highlighted frames for each word
        if highlight_current_word and len(word_times) > 1:
            # List to store the individual word highlight clips
            word_clips = []

            for i, (word_text, word_start, word_end) in enumerate(word_times):
                try:
                    # Find this word in the full text - use a more robust approach
                    # First try exact match
                    word_index = group_text.find(word_text)

                    # If not found, try case-insensitive match
                    if word_index < 0:
                        word_index = group_text.lower().find(word_text.lower())

                    # If still not found, try with punctuation removed
                    if word_index < 0:
                        clean_group_text = ''.join(c for c in group_text if c.isalnum() or c.isspace())
                        clean_word_text = ''.join(c for c in word_text if c.isalnum() or c.isspace())
                        word_index = clean_group_text.find(clean_word_text)

                    # If found, create highlighted image
                    if word_index >= 0:
                        try:
                            # Create a new text image with this word highlighted
                            highlight_img = create_text_image(
                                text=group_text,
                                font_path=font,
                                font_size=font_size,
                                font_color=font_color,
                                stroke_width=stroke_width,
                                stroke_color=stroke_color,
                                image_width=image_width,
                                image_height=font_size * 3,
                                highlight_words=[(word_index, word_index + len(word_text), word_highlight_color)],
                                highlight_style=highlight_style,
                                highlight_bg_color=highlight_bg_color,
                                highlight_bg_opacity=highlight_bg_opacity
                            )
                        except Exception as e:
                            print(f"Error creating highlighted text image: {str(e)}")
                            # Use the base text image instead of a highlighted one
                            highlight_img = text_img
                            print("Using base text image instead of highlighted one")

                        # Convert to numpy array
                        highlight_array = np.array(highlight_img)

                        # Create a clip for this highlighted word
                        highlight_clip = ImageClip(highlight_array, transparent=True)

                        # Set timing for just this word
                        highlight_clip = highlight_clip.set_start(word_start).set_end(word_end)

                        # Set position
                        vertical_padding = padding
                        if position.lower() == "top":
                            highlight_clip = highlight_clip.set_position(('center', vertical_padding))
                        elif position.lower() == "center":
                            highlight_clip = highlight_clip.set_position(('center', 'center'))
                        else:  # "bottom" or default
                            highlight_clip = highlight_clip.set_position(('center', video.h - vertical_padding - highlight_clip.h))

                        # Add to word clips
                        word_clips.append(highlight_clip)
                except Exception as e:
                    print(f"Error processing word highlight for '{word_text}': {str(e)}")
                    # Skip this word highlight but continue with others

            # Convert base image to numpy array for the non-highlighted parts
            text_array = np.array(text_img)
            base_clip = ImageClip(text_array, transparent=True)
            base_clip = base_clip.set_start(start_time).set_end(end_time)

            # Set position
            vertical_padding = padding
            if position.lower() == "top":
                base_clip = base_clip.set_position(('center', vertical_padding))
            elif position.lower() == "center":
                base_clip = base_clip.set_position(('center', 'center'))
            else:  # "bottom" or default
                base_clip = base_clip.set_position(('center', video.h - vertical_padding - base_clip.h))

            # Create gaps for the base clip so it only shows when no word is highlighted
            if word_clips:
                # Create a list of time segments where no word is highlighted
                non_highlighted_segments = []

                # Add segment before first word if needed
                first_word_start = word_clips[0].start
                if start_time < first_word_start:
                    non_highlighted_segments.append((start_time, first_word_start))

                # Add segments between words
                for i in range(len(word_clips) - 1):
                    current_word_end = word_clips[i].end
                    next_word_start = word_clips[i + 1].start
                    if current_word_end < next_word_start:
                        non_highlighted_segments.append((current_word_end, next_word_start))

                # Add segment after last word if needed
                last_word_end = word_clips[-1].end
                if last_word_end < end_time:
                    non_highlighted_segments.append((last_word_end, end_time))

                # Only show base clip during non-highlighted segments
                base_segments = []
                for seg_start, seg_end in non_highlighted_segments:
                    # Create a copy of the base clip for this segment
                    seg_clip = base_clip.copy()
                    seg_clip = seg_clip.set_start(seg_start).set_end(seg_end)
                    base_segments.append(seg_clip)

                # Replace single base clip with segments
                if base_segments:
                    subtitle_clips.extend(base_segments)

                # Add word clips
                subtitle_clips.extend(word_clips)
            else:
                # No word clips, just use the base clip
                subtitle_clips.append(base_clip)
        else:
            # No word highlighting, just use the base text image
            # Convert PIL image to numpy array for MoviePy
            text_array = np.array(text_img)

            # Create clip from numpy array
            txt_clip = ImageClip(text_array, transparent=True)

            # Set position based on the position parameter, ensuring it stays within safe margins
            vertical_padding = padding  # Use the provided padding for vertical position

            if position.lower() == "top":
                txt_clip = txt_clip.set_position(('center', vertical_padding))
            elif position.lower() == "center":
                txt_clip = txt_clip.set_position(('center', 'center'))
            else:  # "bottom" or default
                txt_clip = txt_clip.set_position(('center', video.h - vertical_padding - txt_clip.h))

            # Set timing
            txt_clip = txt_clip.set_start(start_time).set_end(end_time)

            # Add to subtitle clips
            subtitle_clips.append(txt_clip)

        print(f"Added caption: '{group_text}' from {start_time:.2f} to {end_time:.2f}")

    # Create the final video
    print("Creating final video with captions...")

    # Check if we have any subtitle clips
    if not subtitle_clips:
        print("WARNING: No subtitle clips were created. Creating a simple caption for the entire video.")
        # Create captions in smaller chunks to make it more readable
        try:
            # Get the video duration
            video_duration = video.duration

            # If we have a large number of words, divide into multiple captions
            if len(words) > 20:
                # Create multiple captions spread across the video
                num_captions = min(5, len(words) // 10)  # Maximum 5 captions, or 1 per 10 words
                words_per_caption = len(words) // num_captions

                for i in range(num_captions):
                    start_idx = i * words_per_caption
                    end_idx = min((i + 1) * words_per_caption, len(words))

                    # Get the text for this caption
                    caption_words = words[start_idx:end_idx]
                    caption_text = " ".join([
                        w.get('text', w.get('word', str(w))) if isinstance(w, dict)
                        else getattr(w, 'text', getattr(w, 'word', str(w)))
                        for w in caption_words
                    ])

                    # Limit text length if needed
                    if len(caption_text) > 100:
                        caption_text = caption_text[:97] + "..."

                    # Calculate timing for this caption
                    caption_start = (i / num_captions) * video_duration
                    caption_end = ((i + 1) / num_captions) * video_duration

                    # Calculate appropriate font size (smaller for more text)
                    adjusted_font_size = max(24, font_size - (len(caption_text) // 20) * 4)
                    print(f"Caption {i+1}/{num_captions}: {len(caption_text)} chars, font size: {adjusted_font_size}")

                    # Create text image
                    text_img = create_text_image(
                        text=caption_text,
                        font_path=font,
                        font_size=adjusted_font_size,
                        font_color=font_color,
                        stroke_width=stroke_width,
                        stroke_color=stroke_color,
                        image_width=video.w,
                        image_height=adjusted_font_size * 3
                    )

                    # Convert to numpy array
                    text_array = np.array(text_img)

                    # Create clip
                    txt_clip = ImageClip(text_array, transparent=True)

                    # Set position
                    if position.lower() == "top":
                        txt_clip = txt_clip.set_position(('center', padding))
                    elif position.lower() == "center":
                        txt_clip = txt_clip.set_position(('center', 'center'))
                    else:  # "bottom" or default
                        txt_clip = txt_clip.set_position(('center', video.h - padding - txt_clip.h))

                    # Set timing
                    txt_clip = txt_clip.set_start(caption_start).set_end(caption_end)

                    # Add to subtitle clips
                    subtitle_clips.append(txt_clip)

                print(f"Added {num_captions} fallback captions distributed across the video")
            else:
                # For shorter videos, create a single caption (original behavior)
                full_text = " ".join([w.get('text', w.get('word', str(w))) if isinstance(w, dict) else getattr(w, 'text', getattr(w, 'word', str(w))) for w in words])

                # Create a text image for the full text
                adjusted_font_size = max(24, font_size - (len(words) // 4) * 4)
                print(f"Original font size: {font_size}, Adjusted for {len(words)} words: {adjusted_font_size}")

                text_img = create_text_image(
                    text=full_text[:100] + "..." if len(full_text) > 100 else full_text,
                    font_path=font,
                    font_size=adjusted_font_size,
                    font_color=font_color,
                    stroke_width=stroke_width,
                    stroke_color=stroke_color,
                    image_width=video.w,
                    image_height=adjusted_font_size * 3
                )

                # Convert to numpy array
                text_array = np.array(text_img)

                # Create clip
                txt_clip = ImageClip(text_array, transparent=True)

                # Set position
                if position.lower() == "top":
                    txt_clip = txt_clip.set_position(('center', padding))
                elif position.lower() == "center":
                    txt_clip = txt_clip.set_position(('center', 'center'))
                else:  # "bottom" or default
                    txt_clip = txt_clip.set_position(('center', video.h - padding - txt_clip.h))

                # Set timing for the entire video
                txt_clip = txt_clip.set_start(0).set_end(video.duration)

                # Add to subtitle clips
                subtitle_clips.append(txt_clip)
                print(f"Added fallback caption for the entire video")
        except Exception as e:
            print(f"Error creating fallback caption: {str(e)}")

    print(f"Adding {len(subtitle_clips)} caption clips to the video")
    final_video = CompositeVideoClip([video] + subtitle_clips)

    # Write the output file
    print(f"Writing output to: {output_file}")
    final_video.write_videofile(output_file)

    # Close the clips
    video.close()
    final_video.close()

    print("Captioning complete!")

def test_custom_captioner():
    """Test the custom captioning system"""
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Add captions to a video with custom font settings")
    parser.add_argument("--video", help="Path to the video file to process")
    parser.add_argument("--output", help="Path to the output video file")
    parser.add_argument("--font", help="Font name or path to a TTF font file")
    parser.add_argument("--font-size", type=int, default=40, help="Font size (default: 40)")
    parser.add_argument("--font-color", default="yellow", help="Font color (default: yellow)")
    parser.add_argument("--stroke-width", type=int, default=3, help="Stroke width (default: 3)")
    parser.add_argument("--stroke-color", default="black", help="Stroke color (default: black)")
    parser.add_argument("--position", default="bottom", choices=["top", "center", "bottom"], help="Caption position (default: bottom)")
    parser.add_argument("--highlight", default="true", choices=["true", "false"], help="Highlight current word (default: true)")

    args = parser.parse_args()

    # Get script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)

    # Find a sample video if not specified
    video_path = args.video
    if not video_path:
        # Find the first video file in the data directory
        data_dir = os.path.join(project_dir, "data")
        for root, _, files in os.walk(data_dir):
            for file in files:
                if file.endswith(".mp4") and not file.endswith("_subtitle.mp4"):
                    video_path = os.path.join(root, file)
                    break
            if video_path:
                break

    if not video_path or not os.path.exists(video_path):
        print("Error: No video file found for testing")
        return

    # Create output file path if not specified
    output_file = args.output
    if not output_file:
        output_dir = os.path.dirname(video_path)
        output_file = os.path.join(output_dir, "custom_captioned.mp4")

    # Find font file if not specified
    font_file = args.font
    if not font_file:
        # Default to TitanOne
        font_name = "TitanOne"
        font_file = os.path.join(project_dir, "font", f"{font_name}.ttf")
    elif os.path.exists(font_file):
        # It's a file path already
        pass
    else:
        # Try to find in the font directory
        font_path = os.path.join(project_dir, "font", f"{font_file}.ttf")
        if os.path.exists(font_path):
            font_file = font_path
        else:
            print(f"Font file not found: {font_file}")
            font_file = os.path.join(project_dir, "font", "TitanOne.ttf")

    # Convert highlight argument to boolean
    highlight = args.highlight.lower() == "true"

    print(f"Processing video: {video_path}")
    print(f"Output file: {output_file}")
    print(f"Font file: {font_file}")
    print(f"Caption position: {args.position}")
    print(f"Word highlighting: {highlight}")

    # Add captions to the video
    add_captions(
        video_file=video_path,
        output_file=output_file,
        font=font_file,
        font_size=args.font_size,
        font_color=args.font_color,
        stroke_width=args.stroke_width,
        stroke_color=args.stroke_color,
        position=args.position,
        highlight_current_word=highlight
    )

if __name__ == "__main__":
    test_custom_captioner()