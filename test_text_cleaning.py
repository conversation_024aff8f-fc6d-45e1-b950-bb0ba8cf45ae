import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import the clean_text_for_tts function from audio_generator
from audio_generator import clean_text_for_tts

# Test cases with various punctuation and special characters
test_cases = [
    "But here's the secret hack: Add fresh ginger and a...",
    "Step 1: Mix the ingredients; Step 2: Bake at 350°F.",
    "Don't forget to add the salt - it's essential!",
    "The recipe calls for: sugar, flour, and eggs.",
    "Is this working? Let's find out!",
    "This has some #hashtags and #trending topics.",
    "This has emojis 😊 and special characters @$%^&*",
    "This has < > | @ $ % ^ & \\ { } [ ] ~ ` characters.",
]

# Test each case
print("Testing text cleaning function for TTS:")
print("-" * 50)

for i, test in enumerate(test_cases):
    cleaned = clean_text_for_tts(test)
    print(f"Test {i+1}:")
    print(f"Original: '{test}'")
    print(f"Cleaned:  '{cleaned}'")
    print("-" * 50)

print("Test completed.")
