from moviepy.editor import *
import numpy as np
from PIL import Image
import math
import numpy
import cv2


def fade(clip, duration=1, type="both"):
    if type == "in":
        return clip.fadein(duration)
    elif type == "out":
        return clip.fadeout(duration)
    elif type == "both":
        return clip.fadein(duration).fadeout(duration)
    else:
        raise ValueError("type must be 'in', 'out', or 'both'")


def dissolve(clip, fade_duration=0.5):
    """
    Apply a dissolve transition effect to a clip.
    This creates a smooth transition by fading in at the beginning and fading out at the end.

    Args:
        clip: The video clip to apply the effect to
        fade_duration: Duration of the fade effect in seconds (at both start and end)

    Returns:
        The clip with dissolve effect applied
    """
    # Make sure fade_duration is not more than half the clip duration
    max_fade = min(fade_duration, clip.duration / 2)

    # Apply fade in at the beginning and fade out at the end
    return clip.fadein(max_fade).fadeout(max_fade)


def zoom_with_dissolve(clip, mode="in", position="center", speed=3, fade_duration=0.5, audio_fade_in_duration=0.15, audio_fade_out_duration=0.2):
    """
    Apply both zoom and dissolve effects to a clip with enhanced audio transitions.
    This creates a smooth transition with both zoom and fade effects while preventing audio glitches.

    Args:
        clip: The video clip to apply the effects to
        mode: Zoom direction, either "in" or "out"
        position: Position for the zoom effect
        speed: Speed of the zoom effect
        fade_duration: Duration of the visual fade effect in seconds (at both start and end)
        audio_fade_in_duration: Duration of the audio fade-in effect in seconds (default: 0.15)
        audio_fade_out_duration: Duration of the audio fade-out effect in seconds (default: 0.2)

    Returns:
        The clip with both zoom and dissolve effects applied and smooth audio transitions
    """
    # First apply the zoom effect
    zoomed_clip = zoom(clip, mode=mode, position=position, speed=speed)

    # Calculate appropriate fade durations
    # Visual fade can be longer than audio fade for better aesthetics
    max_visual_fade = min(fade_duration, zoomed_clip.duration / 3)

    # Apply visual fade effects
    result_clip = zoomed_clip.fadein(max_visual_fade).fadeout(max_visual_fade)

    # Ensure audio transitions are clean by applying audio fades
    # This helps prevent audio glitches and clicking sounds between scenes
    if hasattr(result_clip, 'audio') and result_clip.audio is not None:
        # Calculate appropriate audio fade durations based on clip length
        # Shorter clips need shorter fades to avoid affecting content
        clip_duration = result_clip.duration

        # Adjust fade durations based on clip length
        if clip_duration < 1.0:
            # Very short clips need very short fades
            max_audio_fade_in = min(0.08, clip_duration / 8)
            max_audio_fade_out = min(0.12, clip_duration / 6)
        elif clip_duration < 2.0:
            # Short clips need shorter fades
            max_audio_fade_in = min(0.12, clip_duration / 6)
            max_audio_fade_out = min(0.15, clip_duration / 5)
        else:
            # Normal clips can use standard fade durations
            max_audio_fade_in = min(audio_fade_in_duration, clip_duration / 5)
            max_audio_fade_out = min(audio_fade_out_duration, clip_duration / 4)

        # Apply audio fade in/out with calculated durations
        print(f"Applying audio transitions - fade in: {max_audio_fade_in:.2f}s, fade out: {max_audio_fade_out:.2f}s")
        result_clip = result_clip.audio_fadein(max_audio_fade_in).audio_fadeout(max_audio_fade_out)

    return result_clip


def shake(clip, effect_duration=1, max_offset=5):
    def shake_effect(get_frame, t):
        frame = get_frame(t)

        # Only apply the effect during the specified duration
        if t < effect_duration:
            dx = np.random.randint(-max_offset, max_offset + 1)
            dy = np.random.randint(-max_offset, max_offset + 1)

            # Convert NumPy array to PIL Image
            pil_image = Image.fromarray(frame)

            # Create a new image with a black background
            result = Image.new("RGB", pil_image.size, (0, 0, 0))

            # Paste the original image with an offset
            result.paste(pil_image, (dx, dy))

            # Convert back to NumPy array
            return np.array(result)
        else:
            return frame

    return clip.fl(shake_effect)


def zoom(clip, mode="in", position="center", speed=3):
    if hasattr(clip, "fps") and clip.fps is not None:
        fps = clip.fps
    else:
        fps = 1

    duration = clip.duration
    total_frames = max(1, int(duration * fps))  # ensure at least 1 frame

    def main(getframe, t):
        frame = getframe(t)
        h, w = frame.shape[:2]
        i = t * fps
        if mode == "out":
            i = total_frames - i
        zoom = 1 + (i * ((0.1 * speed) / total_frames))

        # compute the extra zoom to avoid black bars
        extra_zoom = max(w / (w - 2), h / (h - 2))
        zoom *= extra_zoom

        positions = {
            "center": [(w - (w / zoom)) / 2, (h - (h / zoom)) / 2],
            "left": [0, (h - (h / zoom)) / 2],
            "right": [w - (w / zoom), (h - (h / zoom)) / 2],
            "top": [(w - (w / zoom)) / 2, 0],
            "topleft": [0, 0],
            "topright": [w - (w / zoom), 0],
            "bottom": [(w - (w / zoom)) / 2, h - (h / zoom)],
            "bottomleft": [0, h - (h / zoom)],
            "bottomright": [w - (w / zoom), h - (h / zoom)],
        }
        tx, ty = positions[position]
        M = np.array([[zoom, 0, -tx * zoom], [0, zoom, -ty * zoom]])
        frame = cv2.warpAffine(frame, M, (w, h))
        return frame

    return clip.fl(main)
