"""
Service to generate images using Replicate, Together AI, or FAL AI.
Based on the RapidClips implementation.
"""
import os
import base64
import requests
import replicate
from typing import Optional
from together import Together
import fal_client
import time

class ReplicateImageService:
    """
    Service to interact with the Replicate API for image generation using the Flux model.
    """

    def __init__(self, api_token=None):
        """
        Initialize the service with the Replicate API token.

        Args:
            api_token (str): Replicate API token.
        """
        self.api_token = api_token or os.getenv("REPLICATE_API_KEY", "")
        os.environ["REPLICATE_API_TOKEN"] = self.api_token

    def generate_image(self, prompt, width=720, height=1280, steps=4):
        """
        Generate an image based on the given prompt using the Flux Schnell model.

        Args:
            prompt (str): The prompt describing the image to generate.
            width (int): The width of the generated image.
            height (int): The height of the generated image.
            steps (int): Number of inference steps.

        Returns:
            bytes: The generated image data.
        """
        # Add a negative prompt to prevent text in the generated images
        negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

        # Determine if this is portrait or landscape
        is_portrait = height > width

        # Set aspect ratio based on orientation
        aspect_ratio = "9:16" if is_portrait else "16:9"

        print(f"Replicate service using aspect ratio: {aspect_ratio}")

        input_data = {
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "aspect_ratio": aspect_ratio,  # Use aspect_ratio parameter directly
            "num_inference_steps": steps,
            "guidance_scale": 7.5
        }
        model_id = "black-forest-labs/flux-schnell"

        try:
            print(f"Calling Replicate API with model: {model_id}")
            print(f"Input data: {input_data}")

            output = replicate.run(
                model_id,
                input=input_data
            )

            print(f"Replicate API response type: {type(output)}")

            if isinstance(output, list) and len(output) > 0:
                image_url = output[0]
                print(f"Image URL received: {image_url[:50]}...")
                response = requests.get(image_url)
                response.raise_for_status()
                return response.content
            else:
                print(f"Unexpected response from Replicate API: {output}")
                return None
        except Exception as e:
            print(f"Error in Replicate API call: {str(e)}")
            # Try again with different parameters if there's an issue with dimensions
            try:
                print("Retrying with alternative parameters...")

                # Try with a different aspect ratio format
                # Sometimes the model works better with one format vs another
                alt_aspect_ratio = "0.5625" if aspect_ratio == "9:16" else "1.7778"

                print(f"Trying alternative aspect ratio format: {alt_aspect_ratio}")

                # Alternative input data
                alt_input = {
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "aspect_ratio": alt_aspect_ratio,  # Use numeric aspect ratio
                    "num_inference_steps": steps,
                    "guidance_scale": 7.5
                }

                output = replicate.run(
                    model_id,
                    input=alt_input
                )

                if isinstance(output, list) and len(output) > 0:
                    image_url = output[0]
                    print(f"Retry successful, image URL: {image_url[:50]}...")
                    response = requests.get(image_url)
                    response.raise_for_status()
                    return response.content
            except Exception as retry_error:
                print(f"Retry also failed: {str(retry_error)}")

            return None


class TogetherImageService:
    """
    Service to interact with Together AI API for image generation.
    """

    def __init__(self, api_token=None):
        """
        Initialize the Together AI service with an API token.

        Args:
            api_token (str): Together AI API token.
        """
        self.api_token = api_token or os.getenv("TOGETHER_API_KEY", "")
        self.client = Together(api_key=self.api_token)

    def generate_image(self, prompt, width=720, height=1280, steps=4, model="black-forest-labs/FLUX.1-schnell-Free"):
        """
        Generate an image based on the provided prompt using Together AI's models.

        Args:
            prompt (str): The prompt to guide image generation.
            width (int): Width of the generated image (between 64 and 1792, must be multiple of 16).
            height (int): Height of the generated image (between 64 and 1792, must be multiple of 16).
            steps (int): Number of steps for image generation.
            model (str): The model to use for image generation.

        Returns:
            bytes: The image data in binary format.
        """
        try:
            # Ensure width and height are within the allowed range (64-1792)
            width = max(64, min(width, 1792))
            height = max(64, min(height, 1792))

            # Ensure width and height are multiples of 16
            width = (width // 16) * 16
            height = (height // 16) * 16

            # Add a negative prompt to prevent text in the generated images
            negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

            # Generate image using Together AI
            response = self.client.images.generate(
                prompt=prompt,
                negative_prompt=negative_prompt,
                model=model,
                width=width,
                height=height,
                steps=steps,
                n=1,
                response_format="b64_json",
            )

            # Get base64 encoded image
            image_b64 = response.data[0].b64_json

            # Decode the base64 image
            image_data = base64.b64decode(image_b64)
            return image_data
        except Exception as e:
            raise Exception(f"Error generating image with Together AI: {str(e)}")


class FalAIImageService:
    """
    Service to interact with the FAL AI API for image generation.
    """

    def __init__(self, api_token=None):
        """
        Initialize the FAL AI service with an API token.

        Args:
            api_token (str): FAL AI API token.
        """
        # FAL client expects the key in FAL_KEY environment variable
        self.api_token = api_token or os.getenv("FAL_KEY", "")

        # If FAL_KEY is not set, try FAL_API_KEY as fallback for backward compatibility
        if not self.api_token:
            self.api_token = os.getenv("FAL_API_KEY", "")
            if self.api_token:
                print("WARNING: Using FAL_API_KEY is deprecated. Please use FAL_KEY instead.")

        # Set the API key for FAL client
        if self.api_token:
            os.environ['FAL_KEY'] = self.api_token
        else:
            print("ERROR: No FAL AI API key found. Please set the FAL_KEY environment variable.")

    def generate_image(self, prompt, width=1080, height=1920, steps=4):
        """
        Generate an image based on the given prompt using FAL AI.

        Args:
            prompt (str): The prompt describing the image to generate.
            width (int): The width of the generated image.
            height (int): The height of the generated image.
            steps (int): Number of inference steps.

        Returns:
            bytes: The generated image data.
        """
        try:
            # Add a negative prompt to prevent text in the generated images
            negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

            # Calculate aspect ratio
            aspect_ratio = width / height

            # Determine the appropriate image_size parameter based on aspect ratio
            # FAL AI expects specific string values or a properly formatted object
            if abs(aspect_ratio - 9/16) < 0.1:  # Portrait (9:16)
                image_size = "portrait_16_9"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            elif abs(aspect_ratio - 16/9) < 0.1:  # Landscape (16:9)
                image_size = "landscape_16_9"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            elif abs(aspect_ratio - 4/3) < 0.1:  # 4:3 aspect ratio
                if width > height:
                    image_size = "landscape_4_3"
                else:
                    image_size = "portrait_4_3"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            elif abs(aspect_ratio - 1) < 0.1:  # Square
                image_size = "square_hd"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            else:
                # For custom aspect ratios, use the width/height object format
                # Ensure dimensions are within FAL AI's limits
                max_dimension = 1024  # FAL AI typically has limits on dimensions

                # Scale dimensions to fit within limits while maintaining aspect ratio
                if width > height:
                    if width > max_dimension:
                        scale = max_dimension / width
                        width = max_dimension
                        height = int(height * scale)
                else:
                    if height > max_dimension:
                        scale = max_dimension / height
                        height = max_dimension
                        width = int(width * scale)

                # Use custom dimensions
                image_size = {
                    "width": width,
                    "height": height
                }
                print(f"Using custom dimensions for FAL AI: {width}x{height}")

            try:
                # Submit request to FAL AI
                print(f"Submitting request to FAL AI with prompt: {prompt[:50]}...")
                handler = fal_client.submit(
                    "fal-ai/flux/schnell",
                    arguments={
                        "prompt": prompt,
                        "negative_prompt": negative_prompt,
                        "image_size": image_size,
                        "num_images": 1,
                        "num_inference_steps": steps,
                        "enable_safety_checker": False,
                    },
                )

                print(f"Request submitted, waiting for result...")
                # Get the result
                result = handler.get()

                if result and isinstance(result, dict) and "images" in result:
                    images = result["images"]
                    if isinstance(images, list) and images:
                        image_url = images[0].get("url")
                        if image_url:
                            print(f"Image URL received: {image_url[:50]}...")
                            response = requests.get(image_url)
                            response.raise_for_status()
                            return response.content

                print("No valid image data found in FAL AI response")
                return None

            except Exception as e:
                print(f"Error in FAL AI request: {str(e)}")
                if "422" in str(e) or "400" in str(e):
                    print("Error 422/400: This usually means the parameters are invalid.")
                    print("Trying with predefined image size...")

                    # Try again with a predefined image size based on orientation
                    try:
                        # Use a predefined image size based on orientation
                        if width > height:
                            fallback_image_size = "landscape_16_9"
                        else:
                            fallback_image_size = "portrait_16_9"

                        print(f"Retrying with predefined image size: {fallback_image_size}")

                        handler = fal_client.submit(
                            "fal-ai/flux/schnell",
                            arguments={
                                "prompt": prompt,
                                "negative_prompt": negative_prompt,
                                "image_size": fallback_image_size,
                                "num_images": 1,
                                "num_inference_steps": steps,
                                "enable_safety_checker": False,
                            },
                        )

                        result = handler.get()
                        if result and isinstance(result, dict) and "images" in result:
                            images = result["images"]
                            if isinstance(images, list) and images:
                                image_url = images[0].get("url")
                                if image_url:
                                    print(f"Retry successful with predefined size: {fallback_image_size}")
                                    response = requests.get(image_url)
                                    response.raise_for_status()
                                    return response.content
                    except Exception as retry_error:
                        print(f"Retry with predefined size failed: {str(retry_error)}")

                        # Last resort: try without specifying image_size at all
                        try:
                            print("Trying without image_size parameter...")
                            handler = fal_client.submit(
                                "fal-ai/flux/schnell",
                                arguments={
                                    "prompt": prompt,
                                    "negative_prompt": negative_prompt,
                                    "num_images": 1,
                                    "num_inference_steps": steps,
                                    "enable_safety_checker": False,
                                },
                            )

                            result = handler.get()
                            if result and isinstance(result, dict) and "images" in result:
                                images = result["images"]
                                if isinstance(images, list) and images:
                                    image_url = images[0].get("url")
                                    if image_url:
                                        print("Fallback without image_size succeeded, but aspect ratio may be incorrect")
                                        response = requests.get(image_url)
                                        response.raise_for_status()
                                        return response.content
                        except Exception as last_error:
                            print(f"Final fallback also failed: {str(last_error)}")

                # Re-raise the exception
                raise
        except Exception as e:
            print(f"Error in FAL AI image generation: {e}")
            # Re-raise the exception to be consistent with other services
            raise Exception(f"Error generating image with FAL AI: {str(e)}")


def get_image_service(service_name="replicate", api_token=None):
    """
    Factory function to get the appropriate image service.

    Args:
        service_name (str): The name of the service to use ('replicate', 'together', or 'fal').
        api_token (str): Optional API token to use.

    Returns:
        An instance of the requested image service.
    """
    if service_name.lower() == "together":
        return TogetherImageService(api_token)
    elif service_name.lower() == "fal":
        return FalAIImageService(api_token)
    else:  # Default to replicate
        return ReplicateImageService(api_token)
