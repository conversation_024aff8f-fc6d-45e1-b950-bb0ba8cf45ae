#!/usr/bin/env python3
"""
Test script to verify the modern login dialog layout and ensure all elements are visible
"""

import tkinter as tk
from ttkthemes import ThemedTk
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from auth import show_login_dialog
    print("✓ Successfully imported login dialog")
except ImportError as e:
    print(f"❌ Failed to import login dialog: {e}")
    sys.exit(1)

def test_login_layout():
    """Test the login dialog layout"""
    print("\n🧪 Testing Modern Login Dialog Layout")
    print("=" * 50)
    
    # Create root window
    root = ThemedTk(theme="arc")
    root.title("Login Layout Test")
    root.geometry("400x300")
    root.withdraw()  # Hide the main window
    
    # Center the window
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 400) // 2
    y = (screen_height - 300) // 2
    root.geometry(f"400x300+{x}+{y}")
    
    print("✓ Test window created")
    print("📋 Testing features:")
    print("   • Modern UI design with dark theme")
    print("   • Enhanced floating labels")
    print("   • Email and password input fields")
    print("   • Login and Purchase buttons")
    print("   • Real-time validation feedback")
    print("   • Smooth progress animations")
    print("   • Developer contact information")
    print("")
    
    def test_dialog():
        """Test the login dialog"""
        print("🔐 Opening modern login dialog...")
        try:
            result = show_login_dialog(root)
            
            if result:
                print("✅ Login successful!")
            else:
                print("❌ Login failed or cancelled")
                
        except Exception as e:
            print(f"❌ Error during login test: {e}")
        finally:
            print("\n🏁 Test completed")
            root.quit()
    
    # Start the test after a short delay
    root.after(1000, test_dialog)
    
    # Run the test
    root.mainloop()

if __name__ == "__main__":
    test_login_layout()
