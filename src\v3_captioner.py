"""
V3-style captioning system for videos.
This module provides a reliable approach to adding captions to videos
based on the working implementation from version 3.0.
"""

import os
import tempfile
import subprocess
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from moviepy.editor import (
    VideoFileClip,
    CompositeVideoClip,
    ImageClip
)
from openai import OpenAI
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import ai_client

def get_transcription(video_file: str, api_key: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get transcription with word-level timestamps using the selected AI provider (OpenAI or Groq)"""
    # Extract audio from video file
    temp_audio_file = tempfile.mktemp(suffix=".mp3")

    try:
        # Use ffmpeg to extract audio
        subprocess.call([
            "ffmpeg", "-y", "-i", video_file,
            "-vn", "-acodec", "libmp3lame", "-q:a", "2",
            temp_audio_file
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

        # Get video duration for better time estimation
        video_duration = None
        try:
            from moviepy.editor import VideoFileClip
            video = VideoFileClip(video_file)
            video_duration = video.duration
            video.close()
            print(f"Video duration: {video_duration:.2f} seconds")
        except Exception as e:
            print(f"Could not get video duration: {str(e)}")
            video_duration = None

        # Check which AI provider is currently selected
        current_provider = ai_client.current_provider
        print(f"Getting transcription using {current_provider.upper()} API...")

        # Use our AI client manager to handle transcription with the current provider
        try:
            # Request verbose_json format with word-level timestamps
            print(f"Requesting transcription from {current_provider} with word-level timestamps...")
            transcription = ai_client.transcribe_audio(
                audio_file=temp_audio_file,
                response_format="verbose_json"
            )

            # Debug: Print transcription type and structure
            print(f"Received transcription of type: {type(transcription).__name__}")
            if isinstance(transcription, dict):
                print(f"Dictionary keys: {', '.join(transcription.keys())}")
            elif hasattr(transcription, '__dict__'):
                print(f"Object attributes: {', '.join(dir(transcription))}")

            # Check if transcription has words attribute or key
            words = None

            # Try to get words from attribute (OpenAI style)
            if hasattr(transcription, 'words') and transcription.words:
                print(f"Successfully received word-level transcription from {current_provider} (attribute)")
                words = transcription.words

            # Try to get words from dictionary key (Groq style)
            elif isinstance(transcription, dict) and 'words' in transcription:
                print(f"Successfully received word-level transcription from {current_provider} (dictionary)")
                words = transcription['words']

            # If we found words, return them
            if words:
                print(f"Found {len(words)} words in transcription")
                return words

            # If no words found, try to extract text and create estimated timings
            print(f"Warning: Received transcription from {current_provider} but no word-level data found")

            # Try to get text from attribute or dictionary
            text = None
            if hasattr(transcription, 'text') and transcription.text:
                text = transcription.text
                print(f"Found text attribute with {len(text)} characters")
            elif isinstance(transcription, dict) and 'text' in transcription:
                text = transcription['text']
                print(f"Found text key with {len(text)} characters")

            # If we found text, create estimated word timings
            if text:
                print(f"Creating word-level data from text: {text[:50]}...")
                # Create a simple word structure with estimated timings
                words = []
                text_words = text.split()
                print(f"Text contains {len(text_words)} words")

                # Use video duration if available, otherwise estimate
                if video_duration:
                    total_duration = video_duration
                    print(f"Using video duration for timing: {total_duration:.2f} seconds")
                else:
                    # Estimate duration based on word count (average speaking rate)
                    total_duration = max(5.0, len(text_words) * 0.5)  # ~2 words per second
                    print(f"Using estimated duration for timing: {total_duration:.2f} seconds")

                time_per_word = total_duration / len(text_words) if text_words else 0.5

                for i, word in enumerate(text_words):
                    start_time = i * time_per_word
                    end_time = (i + 1) * time_per_word

                    # Add a small buffer between words
                    if i > 0:
                        start_time -= 0.05
                    if i < len(text_words) - 1:
                        end_time += 0.05

                    words.append({
                        "word": word,
                        "start": start_time,
                        "end": end_time,
                        "text": word
                    })
                print(f"Created {len(words)} word timings from text")
                return words

            # If we can't create word data, fall back to OpenAI
            raise Exception("No text or word-level data available in transcription")

        except Exception as e:
            print(f"Error using {current_provider} for transcription: {str(e)}")
            print("Falling back to OpenAI Whisper API...")

            try:
                # If using AI client fails, fall back to direct OpenAI API call
                client = OpenAI(api_key=api_key)
                with open(temp_audio_file, "rb") as audio_file:
                    transcription = client.audio.transcriptions.create(
                        file=audio_file,
                        model="whisper-1",
                        response_format="verbose_json",
                        timestamp_granularities=["word"]
                    )
                print("Successfully received word-level transcription from OpenAI fallback")
                return transcription.words
            except Exception as fallback_error:
                print(f"Error with OpenAI fallback: {str(fallback_error)}")

                # Try to get text-only transcription as a last resort before giving up
                try:
                    print("Trying text-only transcription as last resort...")
                    with open(temp_audio_file, "rb") as audio_file:
                        text_transcription = client.audio.transcriptions.create(
                            file=audio_file,
                            model="whisper-1",
                            response_format="text"
                        )

                    if text_transcription and hasattr(text_transcription, 'text'):
                        text = text_transcription.text
                        print(f"Got text-only transcription: {text[:50]}...")

                        # Create estimated word timings
                        words = []
                        text_words = text.split()

                        # Use video duration if available, otherwise estimate
                        if video_duration:
                            total_duration = video_duration
                        else:
                            total_duration = max(5.0, len(text_words) * 0.5)

                        time_per_word = total_duration / len(text_words) if text_words else 0.5

                        for i, word in enumerate(text_words):
                            words.append({
                                "word": word,
                                "start": i * time_per_word,
                                "end": (i + 1) * time_per_word,
                                "text": word
                            })
                        print(f"Created {len(words)} word timings from text-only transcription")
                        return words
                except Exception as text_error:
                    print(f"Error with text-only transcription: {str(text_error)}")

                # Create a minimal set of words as a last resort
                print("Creating minimal word data as last resort")
                return [{
                    "word": "[No transcription available]",
                    "start": 0.0,
                    "end": 2.0,
                    "text": "[No transcription available]"
                }]

    finally:
        # Clean up temporary audio file
        if os.path.exists(temp_audio_file):
            os.remove(temp_audio_file)

def create_text_image(
    text: str,
    font_path: str,
    font_size: int,
    font_color: str,
    stroke_width: int,
    stroke_color: str,
    image_width: int,
    image_height: int,
    highlight_words: Optional[List[Tuple[int, int, str]]] = None,
    highlight_style: str = "text_color",
    highlight_bg_color: str = "#3700B3",
    highlight_bg_opacity: float = 0.7
) -> Image.Image:
    """
    Create an image with text, with optional word highlighting.

    Args:
        text: Text to render
        font_path: Path to TTF font file
        font_size: Font size in pixels
        font_color: Font color (name or hex code)
        stroke_width: Width of text outline
        stroke_color: Color of text outline
        image_width: Width of the output image
        image_height: Height of the output image
        highlight_words: Optional list of (start_index, end_index, color) tuples for highlighting specific words
        highlight_style: Style of highlighting ("text_color" or "background")
        highlight_bg_color: Background color for highlighted words when using "background" style
        highlight_bg_opacity: Opacity of the background highlight (0.0 to 1.0)

    Returns:
        PIL Image with rendered text
    """
    # Create a transparent image
    text_img = Image.new("RGBA", (image_width, image_height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(text_img)

    # Calculate horizontal padding (15% of image width on each side)
    h_padding = int(image_width * 0.15)
    max_text_width = image_width - (h_padding * 2)

    # Try to load the font
    try:
        # Auto-adjust font size based on word count and text length
        word_count = len(text.split())
        text_length = len(text)

        # Scale font down for more words or very long text
        adjusted_font_size = font_size
        if word_count > 4:
            # Reduce font size by 10% for each word beyond 4
            reduction_factor = max(0.6, 1.0 - ((word_count - 4) * 0.1))
            adjusted_font_size = int(font_size * reduction_factor)

        # Further adjust for very long text
        if text_length > 100:
            # Reduce by additional amount based on length
            length_factor = max(0.7, 1.0 - ((text_length - 100) / 400))
            adjusted_font_size = int(adjusted_font_size * length_factor)

        # Ensure font size doesn't go below minimum readable size
        adjusted_font_size = max(24, adjusted_font_size)

        print(f"Original font size: {font_size}, Adjusted for {word_count} words: {adjusted_font_size}")

        font = ImageFont.truetype(font_path, adjusted_font_size)
    except OSError:
        # Fallback to default font
        print(f"Font {font_path} not found, using default font")
        font = ImageFont.load_default()

    # If no highlighting is needed, render the regular text with stroke
    if not highlight_words:
        # Add stroke (outline) to text
        if stroke_width > 0:
            # Draw text in multiple positions offset by the stroke width
            for offset_x in range(-stroke_width, stroke_width+1, 2):
                for offset_y in range(-stroke_width, stroke_width+1, 2):
                    if offset_x == 0 and offset_y == 0:
                        continue
                    draw.text(
                        (image_width // 2 + offset_x, image_height // 2 + offset_y),
                        text,
                        font=font,
                        fill=stroke_color,
                        anchor="mm",
                        align="center"
                    )

        # Draw the main text centered
        draw.text(
            (image_width // 2, image_height // 2),
            text,
            font=font,
            fill=font_color,
            anchor="mm",
            align="center"
        )
    else:
        # For word highlighting, we need to calculate text positioning more carefully
        # Get total text size
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]

        # Center position
        x_center = image_width // 2
        y_center = image_height // 2

        # Starting positions for left-aligned text that will be centered
        x_start = x_center - (text_width // 2)
        y_start = y_center - (text_height // 2)

        # First, draw the entire text with stroke for the outline effect
        if stroke_width > 0:
            for offset_x in range(-stroke_width, stroke_width+1, 2):
                for offset_y in range(-stroke_width, stroke_width+1, 2):
                    if offset_x == 0 and offset_y == 0:
                        continue
                    draw.text(
                        (x_start + offset_x, y_start + offset_y),
                        text,
                        font=font,
                        fill=stroke_color
                    )

        # Split text into words to handle highlighting
        words = text.split()
        char_positions = []
        current_pos = 0

        # Calculate position of each word in the text
        for word in words:
            word_len = len(word)
            char_positions.append((current_pos, current_pos + word_len))
            current_pos += word_len + 1  # +1 for the space

        # Now draw each part of the text with appropriate colors
        current_pos = 0

        # We'll draw the text in parts to avoid duplicate rendering
        # Don't draw the entire text first - we'll draw each part individually

        # We need to draw all text parts - both highlighted and non-highlighted
        # First, identify all segments that need to be drawn
        segments = []
        last_end = 0

        # Sort highlight_words by start_idx to ensure we process them in order
        sorted_highlights = sorted(highlight_words, key=lambda x: x[0])

        # Create segments for all parts of the text
        for start_idx, end_idx, highlight_color in sorted_highlights:
            if 0 <= start_idx < len(text) and start_idx < end_idx <= len(text):
                # If there's text before this highlight, add it as a non-highlighted segment
                if start_idx > last_end:
                    segments.append((last_end, start_idx, None))  # None means no highlight

                # Add the highlighted segment
                segments.append((start_idx, end_idx, highlight_color))
                last_end = end_idx

        # Add any remaining text after the last highlight
        if last_end < len(text):
            segments.append((last_end, len(text), None))

        # Now draw each segment
        for start_idx, end_idx, highlight_color in segments:
            segment_text = text[start_idx:end_idx]

            # Calculate position of this segment
            segment_before = text[:start_idx]
            before_bbox = draw.textbbox((0, 0), segment_before, font=font)
            before_width = before_bbox[2] - before_bbox[0]

            # Get the bounding box of the segment text
            segment_bbox = draw.textbbox((0, 0), segment_text, font=font)
            segment_width = segment_bbox[2] - segment_bbox[0]
            segment_height = segment_bbox[3] - segment_bbox[1]

            # If this is a non-highlighted segment, just draw it with the normal color
            if highlight_color is None:
                draw.text(
                    (x_start + before_width, y_start),
                    segment_text,
                    font=font,
                    fill=font_color
                )
                continue

            # For highlighted segments, continue with the existing highlight logic
            highlighted_text = segment_text
            highlighted_width = segment_width
            highlighted_height = segment_height

            if highlight_style == "background":
                # Convert hex color to RGBA with opacity
                bg_color = highlight_bg_color
                if bg_color.startswith('#'):
                    r = int(bg_color[1:3], 16)
                    g = int(bg_color[3:5], 16)
                    b = int(bg_color[5:7], 16)
                    a = int(255 * highlight_bg_opacity)
                    bg_color_rgba = (r, g, b, a)
                else:
                    # For named colors, use a semi-transparent version
                    bg_color_rgba = bg_color + (int(255 * highlight_bg_opacity),)

                # Draw a rounded rectangle background
                # Increase padding around the text for better coverage
                padding_x = int(font_size * 0.15)  # Increased from 0.1 to 0.15
                padding_y = int(font_size * 0.2)   # Increased from 0.05 to 0.2 for better vertical coverage

                # Calculate rectangle coordinates with equal padding on all sides
                # For horizontal positioning
                rect_x1 = int(x_start + before_width - padding_x)  # Convert to int to avoid float issues
                rect_x2 = int(rect_x1 + highlighted_width + (padding_x * 2))  # Convert to int

                # For vertical positioning - use the exact same baseline for all text
                # Get the text descent (how far below baseline text goes)
                # This is critical for proper vertical alignment
                text_descent = segment_bbox[3] - segment_bbox[1]

                # Calculate rectangle coordinates to ensure text sits on same baseline
                rect_y1 = int(y_start - padding_y)  # Top of background
                rect_y2 = int(y_start + text_descent + padding_y)  # Bottom of background

                # Draw the background rounded rectangle
                # Calculate corner radius (20% of font size, but max 10px)
                # Ensure corner_radius is an integer to avoid PIL errors
                corner_radius = int(min(int(font_size * 0.2), 10))

                try:
                    # Create a new image for the rounded rectangle background
                    # Ensure dimensions are integers and at least 1 pixel
                    bg_width = max(1, rect_x2 - rect_x1)
                    bg_height = max(1, rect_y2 - rect_y1)
                    bg_img = Image.new('RGBA', (bg_width, bg_height), (0, 0, 0, 0))
                    bg_draw = ImageDraw.Draw(bg_img)

                    # Ensure corner radius is not too large for the rectangle
                    # Corner radius should not be more than half the smallest dimension
                    max_radius = min(bg_width, bg_height) // 2
                    safe_corner_radius = min(corner_radius, max_radius)

                    # Draw the rounded rectangle on the background image
                    try:
                        bg_draw.rounded_rectangle(
                            [0, 0, bg_width - 1, bg_height - 1],  # Use -1 to stay within bounds
                            radius=safe_corner_radius,
                            fill=bg_color_rgba
                        )
                    except TypeError:
                        # Some versions of PIL require int for radius
                        bg_draw.rounded_rectangle(
                            [0, 0, bg_width - 1, bg_height - 1],
                            radius=int(safe_corner_radius),
                            fill=bg_color_rgba
                        )

                    # Paste the background image onto the main image
                    text_img.paste(bg_img, (rect_x1, rect_y1), bg_img)
                except (AttributeError, TypeError, ValueError) as e:
                    # Fallback for older PIL versions that don't support rounded_rectangle
                    print(f"Warning: Rounded rectangle not supported, using regular rectangle: {e}")
                    # Ensure rectangle coordinates are valid
                    draw.rectangle(
                        [rect_x1, rect_y1, rect_x2, rect_y2],
                        fill=bg_color_rgba
                    )

                # Draw the text over the background with the original color
                # Calculate the horizontal center of the background rectangle
                bg_center_x = int(rect_x1 + (rect_x2 - rect_x1) / 2)

                # Draw the text with precise baseline alignment
                # Use the "ma" anchor (middle-ascender) for consistent baseline alignment
                # This ensures all text sits on the same baseline regardless of highlighting
                try:
                    draw.text(
                        (bg_center_x, y_start),
                        highlighted_text,
                        font=font,
                        fill=font_color,
                        anchor="ma"  # Middle-ascender anchor for consistent baseline alignment
                    )
                except (TypeError, ValueError):
                    # Fallback for older PIL versions that don't support the anchor parameter
                    # Calculate text position manually to align with baseline
                    text_bbox = draw.textbbox((0, 0), highlighted_text, font=font)
                    text_ascent = text_bbox[1]  # Distance from top to baseline

                    # Position text with the same baseline as non-highlighted text
                    draw.text(
                        (bg_center_x - (highlighted_width / 2), y_start - text_ascent),
                        highlighted_text,
                        font=font,
                        fill=font_color
                    )
            else:
                # Default to text color highlighting
                draw.text(
                    (x_start + before_width, y_start),
                    highlighted_text,
                    font=font,
                    fill=highlight_color
                )

    return text_img

def add_captions(
    video_file: str,
    output_file: str,
    font: str = "Arial",
    font_size: int = 40,
    font_color: str = "white",
    stroke_width: int = 3,
    stroke_color: str = "black",
    highlight_current_word: bool = True,
    word_highlight_color: str = "yellow",
    words_per_caption: int = 3,
    line_count: int = 1,  # Keeping for backward compatibility
    padding: int = 70,
    position: str = "bottom",
    animation_style: str = "none",  # Kept for backward compatibility
    api_key: Optional[str] = None,
    highlight_style: str = "text_color",
    highlight_bg_color: str = "#3700B3",
    highlight_bg_opacity: float = 0.7
) -> None:
    """
    Add captions to a video file with custom font rendering and word highlighting.

    Args:
        video_file: Path to the input video file
        output_file: Path to the output video file
        font: Path to a TTF font file or a font name
        font_size: Font size in pixels
        font_color: Font color (name or hex code)
        stroke_width: Width of the text outline
        stroke_color: Color of the text outline
        highlight_current_word: Whether to highlight the current word
        word_highlight_color: Color for the highlighted word
        words_per_caption: Number of words to display at once in a caption
                           Use 0 to display full sentences
        line_count: (Deprecated) Maximum number of lines to display at once
        padding: Padding from the edge of the video in pixels
        position: Position of captions ("top", "center", "bottom")
        animation_style: (Deprecated) Kept for backward compatibility
        api_key: OpenAI API key (optional)
        highlight_style: Style of highlighting ("text_color" or "background")
        highlight_bg_color: Background color for highlighted words when using "background" style
        highlight_bg_opacity: Opacity of the background highlight (0.0 to 1.0)
    """
    # Get video clip
    print(f"Loading video: {video_file}")
    video = VideoFileClip(video_file)

    # Get transcription
    words = get_transcription(video_file, api_key)

    if not words:
        print("No transcription found.")
        return

    print(f"Transcription complete. Found {len(words)} words.")

    if words_per_caption == 0:
        print("Using full sentence mode for captions")
    else:
        print(f"Using {words_per_caption} words per caption")

    # Group words into captions based on words_per_caption or sentences
    word_groups = []
    current_group = []
    current_sentence = []
    last_word_ends_sentence = False

    for word in words:
        # Skip empty words
        if hasattr(word, 'text'):
            text = word.text
        elif hasattr(word, 'word'):
            text = word.word
        else:
            # Try dictionary access as a fallback
            try:
                text = word["text"] if "text" in word else word["word"]
            except (TypeError, KeyError):
                text = str(word)  # Last resort

        if not text or text.strip() == "":
            continue

        # Check if this word ends a sentence (for full sentence mode)
        ends_sentence = text.rstrip().endswith(('.', '!', '?')) or text.rstrip().endswith(('."', '!"', '?"'))

        if words_per_caption == 0:
            # Full sentence mode
            current_sentence.append(word)

            if ends_sentence:
                # End of sentence, add to groups
                word_groups.append(current_sentence)
                current_sentence = []
        else:
            # Word count mode
            current_group.append(word)

            # Create a new group when we reach the desired number of words
            # or at a natural break point (end of sentence)
            if len(current_group) >= words_per_caption or ends_sentence:
                word_groups.append(current_group)
                current_group = []

    # Add any remaining words
    if words_per_caption == 0 and current_sentence:
        word_groups.append(current_sentence)
    elif words_per_caption > 0 and current_group:
        word_groups.append(current_group)

    # Create a list of subtitle clips
    subtitle_clips = []

    # Prepare font parameters
    print(f"Using font: {font}, size: {font_size}, color: {font_color}")
    print(f"Stroke width: {stroke_width}, color: {stroke_color}")
    print(f"Caption position: {position}")

    # Ensure image width matches video width for proper centering
    image_width = video.w

    # First, extract timing information for all groups
    group_timings = []

    for group in word_groups:
        if not group:
            continue

        # Get the text of all words in the group
        group_text = " ".join([
            word.text if hasattr(word, 'text') else
            word.word if hasattr(word, 'word') else
            word.get("text", word.get("word", str(word)))
            for word in group
        ])

        # Get timing from the first and last word in the group
        first_word = group[0]
        last_word = group[-1]

        # Get start time from first word
        if hasattr(first_word, 'start'):
            start_time = first_word.start
        else:
            try:
                start_time = first_word["start"]
            except (TypeError, KeyError):
                start_time = 0

        # Get end time from last word
        if hasattr(last_word, 'end'):
            end_time = last_word.end
        else:
            try:
                end_time = last_word["end"]
            except (TypeError, KeyError):
                end_time = start_time + 0.5 * len(group)  # Estimate duration

        # Calculate duration
        duration = end_time - start_time

        # Skip very short groups
        if duration < 0.1:
            continue

        # Store the group, text, and timing information
        group_timings.append((group, group_text, start_time, end_time))

    # Sort group timings by start time
    group_timings.sort(key=lambda x: x[2])

    # Adjust timings to prevent overlapping subtitles
    # Add a small buffer between captions (50ms)
    CAPTION_BUFFER = 0.05

    # Adjust end times to prevent overlaps
    for i in range(len(group_timings) - 1):
        current_group, current_text, current_start, current_end = group_timings[i]
        next_group, next_text, next_start, next_end = group_timings[i + 1]

        # If current caption ends after next one starts, adjust the end time
        if current_end > next_start:
            # Set current end time to be slightly before next start time
            new_end = max(current_start + 0.1, next_start - CAPTION_BUFFER)
            print(f"Adjusting overlapping caption: '{current_text}' end time {current_end:.2f}s → {new_end:.2f}s")
            group_timings[i] = (current_group, current_text, current_start, new_end)

    # Now process each group with adjusted timings
    for group, group_text, start_time, end_time in group_timings:

        # Calculate duration (may have been adjusted to prevent overlaps)
        duration = end_time - start_time

        # Create individual word timestamps for highlighting
        word_times = []
        for i, word in enumerate(group):
            if i == 0:
                word_start = start_time
            else:
                prev_word = group[i-1]
                if hasattr(prev_word, 'end'):
                    word_start = prev_word.end
                else:
                    try:
                        word_start = prev_word["end"]
                    except (TypeError, KeyError):
                        # Estimate based on even distribution
                        word_start = start_time + (i / len(group)) * duration

            if hasattr(word, 'end'):
                word_end = word.end
            else:
                try:
                    word_end = word["end"]
                except (TypeError, KeyError):
                    # Estimate based on even distribution
                    word_end = start_time + ((i + 1) / len(group)) * duration

            word_text = (
                word.text if hasattr(word, 'text') else
                word.word if hasattr(word, 'word') else
                word.get("text", word.get("word", str(word)))
            )

            # Ensure word timing is within the adjusted caption timing
            word_start = max(start_time, min(word_start, end_time - 0.05))
            word_end = max(word_start + 0.05, min(word_end, end_time))

            word_times.append((word_text, word_start, word_end))

        try:
            font_loaded = False
            text_img = None

            # Create the base text image without highlighted words
            text_img = create_text_image(
                text=group_text,
                font_path=font,
                font_size=font_size,
                font_color=font_color,
                stroke_width=stroke_width,
                stroke_color=stroke_color,
                image_width=image_width,
                image_height=font_size * 3  # Height proportional to font size
            )

            font_loaded = True
            print(f"Font loaded successfully: {font}")
        except Exception as e:
            print(f"Error creating text image: {str(e)}")
            if not font_loaded:
                print("Using fallback font")
                try:
                    # Try with system font as fallback
                    text_img = create_text_image(
                        text=group_text,
                        font_path="Arial",
                        font_size=font_size,
                        font_color=font_color,
                        stroke_width=stroke_width,
                        stroke_color=stroke_color,
                        image_width=image_width,
                        image_height=font_size * 3
                    )
                except Exception as e2:
                    print(f"Failed with fallback font too: {str(e2)}")
                    continue

        if text_img is None:
            print(f"Skipping word group '{group_text}' due to rendering issues")
            continue

        # If highlighting is enabled, create highlighted frames for each word
        if highlight_current_word and len(word_times) > 1:
            # List to store the individual word highlight clips
            word_clips = []

            for i, (word_text, word_start, word_end) in enumerate(word_times):
                # Find this word in the full text
                word_index = group_text.find(word_text)
                if word_index >= 0:
                    # Create a new text image with this word highlighted
                    highlight_img = create_text_image(
                        text=group_text,
                        font_path=font,
                        font_size=font_size,
                        font_color=font_color,
                        stroke_width=stroke_width,
                        stroke_color=stroke_color,
                        image_width=image_width,
                        image_height=font_size * 3,
                        highlight_words=[(word_index, word_index + len(word_text), word_highlight_color)],
                        highlight_style=highlight_style,
                        highlight_bg_color=highlight_bg_color,
                        highlight_bg_opacity=highlight_bg_opacity
                    )

                    # Convert to numpy array
                    highlight_array = np.array(highlight_img)

                    # Create a clip for this highlighted word
                    highlight_clip = ImageClip(highlight_array, transparent=True)

                    # Set timing for just this word
                    highlight_clip = highlight_clip.set_start(word_start).set_end(word_end)

                    # Set position
                    vertical_padding = padding
                    if position.lower() == "top":
                        highlight_clip = highlight_clip.set_position(('center', vertical_padding))
                    elif position.lower() == "center":
                        highlight_clip = highlight_clip.set_position(('center', 'center'))
                    else:  # "bottom" or default
                        highlight_clip = highlight_clip.set_position(('center', video.h - vertical_padding - highlight_clip.h))

                    # Add to word clips
                    word_clips.append(highlight_clip)

            # Convert base image to numpy array for the non-highlighted parts
            text_array = np.array(text_img)
            base_clip = ImageClip(text_array, transparent=True)
            base_clip = base_clip.set_start(start_time).set_end(end_time)

            # Set position
            vertical_padding = padding
            if position.lower() == "top":
                base_clip = base_clip.set_position(('center', vertical_padding))
            elif position.lower() == "center":
                base_clip = base_clip.set_position(('center', 'center'))
            else:  # "bottom" or default
                base_clip = base_clip.set_position(('center', video.h - vertical_padding - base_clip.h))

            # Create gaps for the base clip so it only shows when no word is highlighted
            if word_clips:
                # Create a list of time segments where no word is highlighted
                non_highlighted_segments = []

                # Add segment before first word if needed
                first_word_start = word_clips[0].start
                if start_time < first_word_start:
                    non_highlighted_segments.append((start_time, first_word_start))

                # Add segments between words
                for i in range(len(word_clips) - 1):
                    current_word_end = word_clips[i].end
                    next_word_start = word_clips[i + 1].start
                    if current_word_end < next_word_start:
                        non_highlighted_segments.append((current_word_end, next_word_start))

                # Add segment after last word if needed
                last_word_end = word_clips[-1].end
                if last_word_end < end_time:
                    non_highlighted_segments.append((last_word_end, end_time))

                # Only show base clip during non-highlighted segments
                base_segments = []
                for seg_start, seg_end in non_highlighted_segments:
                    # Create a copy of the base clip for this segment
                    seg_clip = base_clip.copy()
                    seg_clip = seg_clip.set_start(seg_start).set_end(seg_end)
                    base_segments.append(seg_clip)

                # Replace single base clip with segments
                if base_segments:
                    subtitle_clips.extend(base_segments)

                # Add word clips
                subtitle_clips.extend(word_clips)
            else:
                # No word clips, just use the base clip
                subtitle_clips.append(base_clip)

        else:
            # No word highlighting, just use the base text image
            # Convert PIL image to numpy array for MoviePy
            text_array = np.array(text_img)

            # Create clip from numpy array
            txt_clip = ImageClip(text_array, transparent=True)

            # Set position based on the position parameter, ensuring it stays within safe margins
            vertical_padding = padding  # Use the provided padding for vertical position

            if position.lower() == "top":
                txt_clip = txt_clip.set_position(('center', vertical_padding))
            elif position.lower() == "center":
                txt_clip = txt_clip.set_position(('center', 'center'))
            else:  # "bottom" or default
                txt_clip = txt_clip.set_position(('center', video.h - vertical_padding - txt_clip.h))

            # Set timing
            txt_clip = txt_clip.set_start(start_time).set_end(end_time)

            # Add to subtitle clips
            subtitle_clips.append(txt_clip)

        print(f"Added caption: '{group_text}' from {start_time:.2f} to {end_time:.2f}")

    # Check if we have any subtitle clips
    if not subtitle_clips:
        print("WARNING: No subtitle clips were created. Creating a simple caption for the entire video.")
        try:
            # Create a simple caption for the entire video
            simple_text = "No captions available"
            if words and len(words) > 0:
                # Use the first few words as a simple caption
                simple_text = " ".join([
                    w.get('text', w.get('word', str(w))) if isinstance(w, dict)
                    else getattr(w, 'text', getattr(w, 'word', str(w)))
                    for w in words[:min(10, len(words))]
                ])
                if len(words) > 10:
                    simple_text += "..."

            # Create a simple text image
            simple_img = create_text_image(
                text=simple_text,
                font_path="Arial",  # Use Arial for maximum compatibility
                font_size=40,
                font_color="white",
                stroke_width=2,
                stroke_color="black",
                image_width=video.w,
                image_height=120
            )

            # Convert to numpy array
            simple_array = np.array(simple_img)

            # Create clip
            simple_clip = ImageClip(simple_array, transparent=True)

            # Set position
            if position.lower() == "top":
                simple_clip = simple_clip.set_position(('center', padding))
            elif position.lower() == "center":
                simple_clip = simple_clip.set_position(('center', 'center'))
            else:  # "bottom" or default
                simple_clip = simple_clip.set_position(('center', video.h - padding - simple_clip.h))

            # Set timing for the entire video
            simple_clip = simple_clip.set_start(0).set_end(video.duration)

            # Add to subtitle clips
            subtitle_clips.append(simple_clip)
            print(f"Added fallback caption for the entire video")
        except Exception as e:
            print(f"Error creating fallback caption: {str(e)}")

    # Create the final video
    print("Creating final video with captions...")
    print(f"Adding {len(subtitle_clips)} caption clips to the video")
    final_video = CompositeVideoClip([video] + subtitle_clips)

    # Write the output file
    print(f"Writing output to: {output_file}")
    final_video.write_videofile(output_file)

    # Close the clips
    video.close()
    final_video.close()

    print("Captioning complete!")

def test_captioner():
    """Test function for the v3 captioner"""
    import argparse
    import os

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Add captions to a video with custom font settings")
    parser.add_argument("--video", help="Path to the video file to process")
    parser.add_argument("--output", help="Path to the output video file")
    parser.add_argument("--font", help="Font name or path to a TTF font file")
    parser.add_argument("--font-size", type=int, default=40, help="Font size (default: 40)")
    parser.add_argument("--font-color", default="yellow", help="Font color (default: yellow)")
    parser.add_argument("--stroke-width", type=int, default=3, help="Stroke width (default: 3)")
    parser.add_argument("--stroke-color", default="black", help="Stroke color (default: black)")
    parser.add_argument("--position", default="bottom", choices=["top", "center", "bottom"], help="Caption position (default: bottom)")
    parser.add_argument("--highlight", default="true", choices=["true", "false"], help="Highlight current word (default: true)")

    args = parser.parse_args()

    # Get script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)

    # Find a sample video if not specified
    video_path = args.video
    if not video_path:
        # Find the first video file in the data directory
        data_dir = os.path.join(project_dir, "data")
        for root, _, files in os.walk(data_dir):
            for file in files:
                if file.endswith(".mp4") and not file.endswith("_subtitle.mp4"):
                    video_path = os.path.join(root, file)
                    break
            if video_path:
                break

    if not video_path or not os.path.exists(video_path):
        print("Error: No video file found for testing")
        return

    # Create output file path if not specified
    output_file = args.output
    if not output_file:
        output_dir = os.path.dirname(video_path)
        output_file = os.path.join(output_dir, "v3_captioned.mp4")

    # Find font file if not specified
    font_file = args.font
    if not font_file:
        # Default to TitanOne
        font_name = "TitanOne"
        font_file = os.path.join(project_dir, "font", f"{font_name}.ttf")
    elif os.path.exists(font_file):
        # It's a file path already
        pass

    print(f"Testing v3 captioner with video: {video_path}")
    print(f"Output file: {output_file}")
    print(f"Font: {font_file}")

    # Call the captioner
    try:
        add_captions(
            video_file=video_path,
            output_file=output_file,
            font=font_file,
            font_size=args.font_size,
            font_color=args.font_color,
            stroke_width=args.stroke_width,
            stroke_color=args.stroke_color,
            highlight_current_word=args.highlight.lower() == "true",
            words_per_caption=3,
            padding=70,
            position=args.position
        )
        print(f"Captioning completed successfully. Output saved to: {output_file}")
    except Exception as e:
        print(f"Error during captioning: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_captioner()
